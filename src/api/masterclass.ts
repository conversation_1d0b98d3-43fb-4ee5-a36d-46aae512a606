import { AxiosInstance } from ".";
import { STORAGE_KEY } from "../Components/constants";
import {
  MasterclassCreate,
  MasterclassFilters,
  MasterclassPayment,
} from "../types/masterclass";

// Get all masterclasses with filters
export const getMasterclasses = async (
  filters: MasterclassFilters = {},
  skip: string = "0",
  limit: string = "10"
) => {
  try {
    const userData: string =
      localStorage.getItem(STORAGE_KEY)?.split(" ")[0] ?? "";
    const config = {
      headers: {
        Authorization: `Bearer ${userData}`,
      },
    };

    const queryParams = new URLSearchParams({
      skip,
      limit,
      ...Object.fromEntries(
        Object.entries(filters).filter(
          ([_, value]) => value !== undefined && value !== ""
        )
      ),
    });

    const response = await AxiosInstance.get(
      `/masterclass?${queryParams.toString()}`,
      config
    );
    return response;
  } catch (err: any) {
    return err?.response;
  }
};

// Get single masterclass by ID
export const getMasterclassById = async (id: string) => {
  try {
    const userData: string =
      localStorage.getItem(STORAGE_KEY)?.split(" ")[0] ?? "";
    const config = {
      headers: {
        Authorization: `Bearer ${userData}`,
      },
    };
    const response = await AxiosInstance.get(`/masterclass/${id}`, config);
    return response;
  } catch (err: any) {
    return err?.response;
  }
};

// Create new masterclass (admin only)
export const createMasterclass = async (data: MasterclassCreate) => {
  try {
    const userData: string =
      localStorage.getItem(STORAGE_KEY)?.split(" ")[0] ?? "";
    const config = {
      headers: {
        Authorization: `Bearer ${userData}`,
      },
    };
    const response = await AxiosInstance.post("/masterclass", data, config);
    return response;
  } catch (err: any) {
    return err?.response;
  }
};

// Update masterclass (admin only)
export const updateMasterclass = async (
  id: string,
  data: Partial<MasterclassCreate>
) => {
  try {
    const userData: string =
      localStorage.getItem(STORAGE_KEY)?.split(" ")[0] ?? "";
    const config = {
      headers: {
        Authorization: `Bearer ${userData}`,
      },
    };
    const response = await AxiosInstance.put(
      `/masterclass/${id}`,
      data,
      config
    );
    return response;
  } catch (err: any) {
    return err?.response;
  }
};

// Delete masterclass (admin only)
export const deleteMasterclass = async (id: string) => {
  try {
    const userData: string =
      localStorage.getItem(STORAGE_KEY)?.split(" ")[0] ?? "";
    const config = {
      headers: {
        Authorization: `Bearer ${userData}`,
      },
    };
    const response = await AxiosInstance.delete(`/masterclass/${id}`, config);
    return response;
  } catch (err: any) {
    return err?.response;
  }
};

// Enroll in free masterclass
export const enrollInMasterclass = async (masterclassId: string) => {
  try {
    const userData: string =
      localStorage.getItem(STORAGE_KEY)?.split(" ")[0] ?? "";
    const config = {
      headers: {
        Authorization: `Bearer ${userData}`,
      },
    };
    const response = await AxiosInstance.post(
      `/masterclass/${masterclassId}/enroll`,
      {},
      config
    );
    return response;
  } catch (err: any) {
    return err?.response;
  }
};

// Create payment order for paid masterclass
export const createMasterclassPayment = async (
  masterclassId: string
): Promise<MasterclassPayment> => {
  try {
    const userData: string =
      localStorage.getItem(STORAGE_KEY)?.split(" ")[0] ?? "";
    const config = {
      headers: {
        Authorization: `Bearer ${userData}`,
      },
    };
    const response = await AxiosInstance.post(
      `/masterclass/${masterclassId}/payment`,
      {},
      config
    );
    return response.data;
  } catch (err: any) {
    throw err?.response;
  }
};

// Get user's enrolled masterclasses
export const getUserMasterclasses = async (
  skip: string = "0",
  limit: string = "10"
) => {
  try {
    const userData: string =
      localStorage.getItem(STORAGE_KEY)?.split(" ")[0] ?? "";
    const config = {
      headers: {
        Authorization: `Bearer ${userData}`,
      },
    };
    const response = await AxiosInstance.get(
      `/masterclass/user/enrolled?skip=${skip}&limit=${limit}`,
      config
    );
    return response;
  } catch (err: any) {
    return err?.response;
  }
};

// Check if user has access to masterclass
export const checkMasterclassAccess = async (masterclassId: string) => {
  try {
    const userData: string =
      localStorage.getItem(STORAGE_KEY)?.split(" ")[0] ?? "";
    const config = {
      headers: {
        Authorization: `Bearer ${userData}`,
      },
    };
    const response = await AxiosInstance.get(
      `/masterclass/${masterclassId}/access`,
      config
    );
    return response;
  } catch (err: any) {
    return err?.response;
  }
};

// Update masterclass progress
export const updateMasterclassProgress = async (
  masterclassId: string,
  progress: number
) => {
  try {
    const userData: string =
      localStorage.getItem(STORAGE_KEY)?.split(" ")[0] ?? "";
    const config = {
      headers: {
        Authorization: `Bearer ${userData}`,
      },
    };
    const response = await AxiosInstance.put(
      `/masterclass/${masterclassId}/progress`,
      { progress },
      config
    );
    return response;
  } catch (err: any) {
    return err?.response;
  }
};

// Get masterclass statistics (admin only)
export const getMasterclassStats = async () => {
  try {
    const userData: string =
      localStorage.getItem(STORAGE_KEY)?.split(" ")[0] ?? "";
    const config = {
      headers: {
        Authorization: `Bearer ${userData}`,
      },
    };
    const response = await AxiosInstance.get("/masterclass/stats", config);
    return response;
  } catch (err: any) {
    return err?.response;
  }
};

// Get featured/popular masterclasses
export const getFeaturedMasterclasses = async (limit: string = "6") => {
  try {
    const userData: string =
      localStorage.getItem(STORAGE_KEY)?.split(" ")[0] ?? "";
    const config = {
      headers: {
        Authorization: `Bearer ${userData}`,
      },
    };
    const response = await AxiosInstance.get(
      `/masterclass/featured?limit=${limit}`,
      config
    );
    return response;
  } catch (err: any) {
    return err?.response;
  }
};

// Search masterclasses
export const searchMasterclasses = async (
  query: string,
  skip: string = "0",
  limit: string = "10"
) => {
  try {
    const userData: string =
      localStorage.getItem(STORAGE_KEY)?.split(" ")[0] ?? "";
    const config = {
      headers: {
        Authorization: `Bearer ${userData}`,
      },
    };
    const response = await AxiosInstance.get(
      `/masterclass/search?q=${encodeURIComponent(
        query
      )}&skip=${skip}&limit=${limit}`,
      config
    );
    return response;
  } catch (err: any) {
    return err?.response;
  }
};
