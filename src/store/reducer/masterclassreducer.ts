import * as actionTypes from "../actiontypes/actionTypes";
import { MASTERCLASS_STATE, MASTERCLASS_ACTION } from "../../types/masterclass";

// Dummy data for testing
const dummyMasterclasses = [
  {
    _id: "1",
    title: "Advanced React Development",
    description:
      "Master advanced React concepts including hooks, context, performance optimization, and modern patterns. This comprehensive course covers everything you need to become a React expert.",
    shortDescription:
      "Learn advanced React concepts and modern development patterns",
    instructor: "<PERSON>",
    instructor<PERSON><PERSON>:
      "Senior Frontend Developer at Google with 8+ years of React experience",
    instructorImage:
      "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
    duration: 180,
    level: "ADVANCED" as const,
    category: "Technology",
    tags: ["React", "JavaScript", "Frontend", "Hooks"],
    thumbnailImage:
      "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=300&fit=crop",
    videoUrl:
      "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",
    price: 99.99,
    currency: "USD",
    isPaid: true,
    isActive: true,
    createdBy: "admin",
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
    enrollmentCount: 1250,
    rating: 4.8,
    reviewCount: 324,
    prerequisites: ["Basic React knowledge", "JavaScript ES6+"],
    learningOutcomes: [
      "Master React Hooks",
      "Optimize React performance",
      "Build scalable applications",
    ],
    resources: [
      { title: "Course Materials", url: "#", type: "PDF" as const },
      { title: "Code Repository", url: "#", type: "LINK" as const },
    ],
  },
  {
    _id: "2",
    title: "Machine Learning Fundamentals",
    description:
      "A comprehensive introduction to machine learning concepts, algorithms, and practical applications. Perfect for beginners looking to enter the field of AI and data science.",
    shortDescription: "Learn the fundamentals of machine learning and AI",
    instructor: "Dr. Michael Chen",
    instructorBio: "AI Research Scientist at Stanford University",
    instructorImage:
      "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
    duration: 240,
    level: "BEGINNER" as const,
    category: "AI & Machine Learning",
    tags: ["Machine Learning", "Python", "Data Science", "AI"],
    thumbnailImage:
      "https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=400&h=300&fit=crop",
    videoUrl:
      "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",
    price: 0,
    currency: "USD",
    isPaid: false,
    isActive: true,
    createdBy: "admin",
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
    enrollmentCount: 3420,
    rating: 4.6,
    reviewCount: 892,
    prerequisites: ["Basic programming knowledge"],
    learningOutcomes: [
      "Understand ML algorithms",
      "Build your first ML model",
      "Apply ML to real problems",
    ],
    resources: [
      { title: "Python Setup Guide", url: "#", type: "PDF" as const },
      { title: "Dataset Collection", url: "#", type: "LINK" as const },
    ],
  },
  {
    _id: "3",
    title: "Digital Marketing Strategy",
    description:
      "Learn how to create and execute effective digital marketing campaigns across multiple channels. Covers SEO, social media, content marketing, and analytics.",
    shortDescription:
      "Master digital marketing strategies and campaign management",
    instructor: "Emma Rodriguez",
    instructorBio: "Digital Marketing Director with 10+ years experience",
    instructorImage:
      "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
    duration: 150,
    level: "INTERMEDIATE" as const,
    category: "Marketing",
    tags: ["Digital Marketing", "SEO", "Social Media", "Analytics"],
    thumbnailImage:
      "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop",
    videoUrl:
      "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",
    price: 79.99,
    currency: "USD",
    isPaid: true,
    isActive: true,
    createdBy: "admin",
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
    enrollmentCount: 2100,
    rating: 4.7,
    reviewCount: 456,
    prerequisites: ["Basic marketing knowledge"],
    learningOutcomes: [
      "Create marketing strategies",
      "Analyze campaign performance",
      "Optimize ROI",
    ],
    resources: [
      { title: "Marketing Templates", url: "#", type: "PDF" as const },
      { title: "Analytics Tools Guide", url: "#", type: "LINK" as const },
    ],
  },
  {
    _id: "4",
    title: "UI/UX Design Principles",
    description:
      "Comprehensive guide to user interface and user experience design. Learn design thinking, prototyping, and creating user-centered digital experiences.",
    shortDescription: "Learn modern UI/UX design principles and best practices",
    instructor: "Alex Thompson",
    instructorBio:
      "Lead UX Designer at Apple, former Google Design team member",
    instructorImage:
      "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
    duration: 200,
    level: "INTERMEDIATE" as const,
    category: "Design",
    tags: ["UI Design", "UX Design", "Figma", "Prototyping"],
    thumbnailImage:
      "https://images.unsplash.com/photo-1561070791-2526d30994b5?w=400&h=300&fit=crop",
    videoUrl:
      "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",
    price: 89.99,
    currency: "USD",
    isPaid: true,
    isActive: true,
    createdBy: "admin",
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
    enrollmentCount: 1800,
    rating: 4.9,
    reviewCount: 267,
    prerequisites: ["Basic design knowledge", "Figma familiarity"],
    learningOutcomes: [
      "Master design principles",
      "Create user personas",
      "Build interactive prototypes",
    ],
    resources: [
      { title: "Design System Template", url: "#", type: "PDF" as const },
      { title: "Figma Resources", url: "#", type: "LINK" as const },
    ],
  },
  {
    _id: "5",
    title: "Blockchain Development",
    description:
      "Learn blockchain technology from the ground up. Build decentralized applications, understand smart contracts, and explore cryptocurrency development.",
    shortDescription:
      "Master blockchain technology and smart contract development",
    instructor: "David Kim",
    instructorBio: "Blockchain Engineer at Ethereum Foundation",
    instructorImage:
      "https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=150&h=150&fit=crop&crop=face",
    duration: 300,
    level: "ADVANCED" as const,
    category: "Technology",
    tags: ["Blockchain", "Ethereum", "Smart Contracts", "Web3"],
    thumbnailImage:
      "https://images.unsplash.com/photo-1639762681485-074b7f938ba0?w=400&h=300&fit=crop",
    videoUrl:
      "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",
    price: 149.99,
    currency: "USD",
    isPaid: true,
    isActive: true,
    createdBy: "admin",
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
    enrollmentCount: 890,
    rating: 4.5,
    reviewCount: 178,
    prerequisites: ["Programming experience", "JavaScript knowledge"],
    learningOutcomes: [
      "Build DApps",
      "Deploy smart contracts",
      "Understand Web3",
    ],
    resources: [
      { title: "Solidity Guide", url: "#", type: "PDF" as const },
      { title: "Development Tools", url: "#", type: "LINK" as const },
    ],
  },
  {
    _id: "6",
    title: "Data Science with Python",
    description:
      "Complete data science course covering Python programming, data analysis, visualization, and machine learning. Perfect for aspiring data scientists.",
    shortDescription: "Learn data science and analytics using Python",
    instructor: "Dr. Lisa Wang",
    instructorBio: "Data Science Lead at Netflix, PhD in Statistics",
    instructorImage:
      "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
    duration: 220,
    level: "BEGINNER" as const,
    category: "Data Science",
    tags: ["Python", "Data Analysis", "Pandas", "Visualization"],
    thumbnailImage:
      "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop",
    videoUrl:
      "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",
    price: 0,
    currency: "USD",
    isPaid: false,
    isActive: true,
    createdBy: "admin",
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
    enrollmentCount: 4200,
    rating: 4.7,
    reviewCount: 1024,
    prerequisites: ["Basic programming knowledge"],
    learningOutcomes: [
      "Master Python for data science",
      "Create data visualizations",
      "Build predictive models",
    ],
    resources: [
      { title: "Python Cheat Sheet", url: "#", type: "PDF" as const },
      { title: "Dataset Library", url: "#", type: "LINK" as const },
    ],
  },
];

const initialState: MASTERCLASS_STATE = {
  MASTERCLASS_LIST: {
    masterclasses: dummyMasterclasses,
    masterclassCount: dummyMasterclasses.length,
  },
  USER_MASTERCLASS_LIST: {
    enrolledMasterclasses: [
      {
        masterclass: dummyMasterclasses[1], // Machine Learning Fundamentals (free)
        enrollment: {
          _id: "enroll1",
          userId: "user1",
          masterclassId: "2",
          enrolledAt: "2024-01-15T10:00:00Z",
          progress: 75,
          isCompleted: false,
          paymentId: undefined,
        },
      },
      {
        masterclass: dummyMasterclasses[5], // Data Science with Python (free)
        enrollment: {
          _id: "enroll2",
          userId: "user1",
          masterclassId: "6",
          enrolledAt: "2024-01-10T14:30:00Z",
          progress: 100,
          isCompleted: true,
          paymentId: undefined,
          completedAt: "2024-01-20T16:45:00Z",
        },
      },
      {
        masterclass: dummyMasterclasses[0], // Advanced React Development (paid)
        enrollment: {
          _id: "enroll3",
          userId: "user1",
          masterclassId: "1",
          enrolledAt: "2024-01-20T09:15:00Z",
          progress: 25,
          isCompleted: false,
          paymentId: "pay_123456789",
        },
      },
    ],
    enrollmentCount: 3,
  },
  CURRENT_MASTERCLASS: null,
  MASTERCLASS_STATS: {
    totalMasterclasses: dummyMasterclasses.length,
    totalEnrollments: 13660,
    totalRevenue: 45280.5,
    averageRating: 4.7,
  },
  LOADING: false,
  ERROR: null,
};

export const masterclassreducer = (
  state: MASTERCLASS_STATE = initialState,
  action: MASTERCLASS_ACTION
): MASTERCLASS_STATE => {
  switch (action.type) {
    case actionTypes.MASTERCLASS_LOADING:
      return {
        ...state,
        LOADING: action.payload,
        ERROR: null,
      };

    case actionTypes.MASTERCLASS_ERROR:
      return {
        ...state,
        LOADING: false,
        ERROR: action.payload,
      };

    case actionTypes.MASTERCLASS_GET:
      return {
        ...state,
        MASTERCLASS_LIST: action.payload,
        LOADING: false,
        ERROR: null,
      };

    case actionTypes.MASTERCLASS_GET_ID:
      return {
        ...state,
        CURRENT_MASTERCLASS: action.payload,
        LOADING: false,
        ERROR: null,
      };

    case actionTypes.MASTERCLASS_CREATE:
      return {
        ...state,
        MASTERCLASS_LIST: {
          ...state.MASTERCLASS_LIST,
          masterclasses: [
            action.payload,
            ...state.MASTERCLASS_LIST.masterclasses,
          ],
          masterclassCount: state.MASTERCLASS_LIST.masterclassCount + 1,
        },
        LOADING: false,
        ERROR: null,
      };

    case actionTypes.MASTERCLASS_UPDATE:
      return {
        ...state,
        MASTERCLASS_LIST: {
          ...state.MASTERCLASS_LIST,
          masterclasses: state.MASTERCLASS_LIST.masterclasses.map(
            (masterclass) =>
              masterclass._id === action.payload._id
                ? action.payload
                : masterclass
          ),
        },
        CURRENT_MASTERCLASS:
          state.CURRENT_MASTERCLASS?._id === action.payload._id
            ? action.payload
            : state.CURRENT_MASTERCLASS,
        LOADING: false,
        ERROR: null,
      };

    case actionTypes.MASTERCLASS_DELETE:
      return {
        ...state,
        MASTERCLASS_LIST: {
          ...state.MASTERCLASS_LIST,
          masterclasses: state.MASTERCLASS_LIST.masterclasses.filter(
            (masterclass) => masterclass._id !== action.payload
          ),
          masterclassCount: state.MASTERCLASS_LIST.masterclassCount - 1,
        },
        CURRENT_MASTERCLASS:
          state.CURRENT_MASTERCLASS?._id === action.payload
            ? null
            : state.CURRENT_MASTERCLASS,
        LOADING: false,
        ERROR: null,
      };

    case actionTypes.MASTERCLASS_USER_LIST:
      return {
        ...state,
        USER_MASTERCLASS_LIST: action.payload,
        LOADING: false,
        ERROR: null,
      };

    case actionTypes.MASTERCLASS_ENROLL:
      return {
        ...state,
        USER_MASTERCLASS_LIST: {
          ...state.USER_MASTERCLASS_LIST,
          enrolledMasterclasses: [
            action.payload,
            ...state.USER_MASTERCLASS_LIST.enrolledMasterclasses,
          ],
          enrollmentCount: state.USER_MASTERCLASS_LIST.enrollmentCount + 1,
        },
        LOADING: false,
        ERROR: null,
      };

    case actionTypes.MASTERCLASS_STATS:
      return {
        ...state,
        MASTERCLASS_STATS: action.payload,
        LOADING: false,
        ERROR: null,
      };

    default:
      return state;
  }
};
