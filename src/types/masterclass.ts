export interface MasterclassItem {
  _id?: string;
  title: string;
  description: string;
  shortDescription: string;
  instructor: string;
  instructorBio?: string;
  instructorImage?: string;
  duration: number; // in minutes
  level: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED';
  category: string;
  tags: string[];
  thumbnailImage: string;
  videoUrl: string;
  price: number; // 0 for free masterclasses
  currency: string;
  isPaid: boolean;
  isActive: boolean;
  sectorId?: string;
  subSectorId?: string;
  createdBy: string;
  createdAt?: string;
  updatedAt?: string;
  enrollmentCount?: number;
  rating?: number;
  reviewCount?: number;
  prerequisites?: string[];
  learningOutcomes?: string[];
  resources?: Array<{
    title: string;
    url: string;
    type: 'PDF' | 'LINK' | 'VIDEO';
  }>;
}

export interface MasterclassCreate {
  title: string;
  description: string;
  shortDescription: string;
  instructor: string;
  instructorBio?: string;
  instructorImage?: string;
  duration: number;
  level: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED';
  category: string;
  tags: string[];
  thumbnailImage: string;
  videoUrl: string;
  price: number;
  currency: string;
  isPaid: boolean;
  sectorId?: string;
  subSectorId?: string;
  prerequisites?: string[];
  learningOutcomes?: string[];
  resources?: Array<{
    title: string;
    url: string;
    type: 'PDF' | 'LINK' | 'VIDEO';
  }>;
}

export interface MasterclassEnrollment {
  _id?: string;
  userId: string;
  masterclassId: string;
  enrolledAt: string;
  completedAt?: string;
  progress: number; // percentage 0-100
  isCompleted: boolean;
  paymentId?: string;
  accessExpiresAt?: string;
}

export interface MasterclassPayment {
  masterclassId: string;
  amount: number;
  currency: string;
  order_id: string;
}

export interface MasterclassList {
  masterclasses: MasterclassItem[];
  masterclassCount: number;
}

export interface UserMasterclassList {
  enrolledMasterclasses: Array<{
    masterclass: MasterclassItem;
    enrollment: MasterclassEnrollment;
  }>;
  enrollmentCount: number;
}

export interface MasterclassFilters {
  category?: string;
  level?: string;
  isPaid?: boolean;
  sectorId?: string;
  subSectorId?: string;
  search?: string;
  sortBy?: 'newest' | 'oldest' | 'price_low' | 'price_high' | 'rating' | 'popular';
}

export interface MasterclassStats {
  totalMasterclasses: number;
  totalEnrollments: number;
  totalRevenue: number;
  averageRating: number;
}

// Redux Types
export interface MASTERCLASS_STATE {
  MASTERCLASS_LIST: MasterclassList;
  USER_MASTERCLASS_LIST: UserMasterclassList;
  CURRENT_MASTERCLASS: MasterclassItem | null;
  MASTERCLASS_STATS: MasterclassStats;
  LOADING: boolean;
  ERROR: string | null;
}

export interface MASTERCLASS_ACTION {
  type: string;
  payload?: any;
  MASTERCLASS?: any;
}

export type MASTERCLASS_DISPATCH = (args: MASTERCLASS_ACTION) => MASTERCLASS_ACTION;
