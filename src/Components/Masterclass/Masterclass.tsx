import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link, useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet";
import { getAllMasterclasses } from "../../store/actioncreators/masterclassactions";
import { MasterclassFilters } from "../../types/masterclass";
import { title } from "../constants";
import MasterclassCard from "./MasterclassCard";
import MasterclassFiltersComponent from "./MasterclassFilters";
import LoadingSpinner from "../Loader/LoadingSpinner";

interface MasterclassProps {
  handleLoginModal: () => void;
}

const Masterclass: React.FC<MasterclassProps> = ({ handleLoginModal }) => {
  const dispatch: any = useDispatch();
  const navigate = useNavigate();

  const { MASTERCLASS_LIST, LOADING, ERROR } = useSelector(
    (state: any) => state.MASTERCLASS
  );
  const currentUser = useSelector((state: any) => state.USER.USER);

  const [filters, setFilters] = useState<MasterclassFilters>({});
  const [skip, setSkip] = useState(0);
  const [limit] = useState(12);
  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    dispatch(getAllMasterclasses(filters, skip.toString(), limit.toString()));
  }, [dispatch, filters, skip, limit]);

  const handleFilterChange = (newFilters: MasterclassFilters) => {
    setFilters(newFilters);
    setSkip(0); // Reset pagination when filters change
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setFilters({ ...filters, search: query });
    setSkip(0);
  };

  const handleLoadMore = () => {
    setSkip(skip + limit);
  };

  const handleMasterclassClick = (masterclassId: string) => {
    if (currentUser.admin === -1) {
      handleLoginModal();
      return;
    }
    navigate(`/masterclass/${masterclassId}`);
  };

  return (
    <>
      <Helmet>
        <title>{title.MASTERCLASS}</title>
        <meta
          name="description"
          content="Explore our comprehensive masterclass collection featuring expert-led courses on various topics. Learn from industry professionals and advance your skills."
        />
      </Helmet>

      <div className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white">
          <div className="w-full px-4 sm:px-6 lg:px-8 py-16">
            <div className="text-center">
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                Master New Skills with Expert-Led Classes
              </h1>
              <p className="text-xl md:text-2xl mb-8 text-blue-100">
                Learn from industry professionals and advance your career with
                our comprehensive masterclass collection
              </p>

              {/* Search Bar */}
              <div className="max-w-2xl mx-auto">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search masterclasses..."
                    value={searchQuery}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="w-full px-6 py-4 text-gray-900 rounded-full text-lg focus:outline-none focus:ring-4 focus:ring-blue-300"
                  />
                  <button className="absolute right-2 top-2 bg-blue-600 text-white p-2 rounded-full hover:bg-blue-700 transition-colors">
                    <svg
                      className="w-6 h-6"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                      />
                    </svg>
                  </button>
                </div>
              </div>

              {/* Admin Actions */}
              {currentUser.admin === 0 && (
                <div className="mt-8 text-center">
                  <button
                    onClick={() => navigate("/create-masterclass")}
                    className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors font-medium"
                  >
                    + Create New Masterclass
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="w-full px-4 sm:px-6 lg:px-8 py-12">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Filters Sidebar */}
            <div className="lg:w-1/5">
              <MasterclassFiltersComponent
                filters={filters}
                onFilterChange={handleFilterChange}
              />
            </div>

            {/* Masterclass Grid */}
            <div className="lg:w-4/5">
              {LOADING && skip === 0 ? (
                <div className="flex justify-center items-center h-64">
                  <LoadingSpinner />
                </div>
              ) : ERROR ? (
                <div className="text-center py-12">
                  <div className="text-red-600 text-lg mb-4">{ERROR}</div>
                  <button
                    onClick={() =>
                      dispatch(
                        getAllMasterclasses(filters, "0", limit.toString())
                      )
                    }
                    className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Try Again
                  </button>
                </div>
              ) : (
                <>
                  {/* Results Header */}
                  <div className="flex justify-between items-center mb-6">
                    <h2 className="text-2xl font-bold text-gray-900">
                      {MASTERCLASS_LIST.masterclassCount > 0
                        ? `${MASTERCLASS_LIST.masterclassCount} Masterclasses Found`
                        : "No Masterclasses Found"}
                    </h2>

                    {/* Sort Dropdown */}
                    <select
                      value={filters.sortBy || "newest"}
                      onChange={(e) =>
                        handleFilterChange({
                          ...filters,
                          sortBy: e.target.value as any,
                        })
                      }
                      className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="newest">Newest First</option>
                      <option value="oldest">Oldest First</option>
                      <option value="price_low">Price: Low to High</option>
                      <option value="price_high">Price: High to Low</option>
                      <option value="rating">Highest Rated</option>
                      <option value="popular">Most Popular</option>
                    </select>
                  </div>

                  {/* Masterclass Grid */}
                  {MASTERCLASS_LIST.masterclasses.length > 0 ? (
                    <>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-3 gap-8">
                        {MASTERCLASS_LIST.masterclasses.map(
                          (masterclass: any) => (
                            <MasterclassCard
                              key={masterclass._id}
                              masterclass={masterclass}
                              onClick={() =>
                                handleMasterclassClick(masterclass._id!)
                              }
                            />
                          )
                        )}
                      </div>

                      {/* Load More Button */}
                      {MASTERCLASS_LIST.masterclasses.length <
                        MASTERCLASS_LIST.masterclassCount && (
                        <div className="text-center mt-12">
                          <button
                            onClick={handleLoadMore}
                            disabled={LOADING}
                            className="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            {LOADING ? "Loading..." : "Load More"}
                          </button>
                        </div>
                      )}
                    </>
                  ) : (
                    <div className="text-center py-12">
                      <div className="text-gray-500 text-lg mb-4">
                        No masterclasses found matching your criteria.
                      </div>
                      <button
                        onClick={() => {
                          setFilters({});
                          setSearchQuery("");
                        }}
                        className="text-blue-600 hover:text-blue-800 font-medium"
                      >
                        Clear all filters
                      </button>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Masterclass;
