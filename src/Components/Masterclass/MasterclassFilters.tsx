import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { MasterclassFilters as FilterType } from "../../types/masterclass";

interface MasterclassFiltersProps {
  filters: FilterType;
  onFilterChange: (filters: FilterType) => void;
}

const MasterclassFilters: React.FC<MasterclassFiltersProps> = ({
  filters,
  onFilterChange,
}) => {
  const [localFilters, setLocalFilters] = useState<FilterType>(filters);
  const [isExpanded, setIsExpanded] = useState(false);

  // Get sectors and subsectors from Redux store
  const sectors = useSelector((state: any) => state.SECTOR.SECTOR_LIST);
  const subSectors = useSelector(
    (state: any) => state.SUB_SECTOR.SUB_SECTOR_LIST
  );

  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  const handleFilterChange = (key: keyof FilterType, value: any) => {
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);
    onFilterChange(newFilters);
  };

  const clearFilters = () => {
    const clearedFilters = {};
    setLocalFilters(clearedFilters);
    onFilterChange(clearedFilters);
  };

  const categories = [
    "Technology",
    "Business",
    "Design",
    "Marketing",
    "Development",
    "Data Science",
    "AI & Machine Learning",
    "Entrepreneurship",
    "Leadership",
    "Innovation",
  ];

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      {/* Mobile Toggle */}
      <div className="lg:hidden mb-4">
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="w-full flex items-center justify-between text-left font-semibold text-gray-900"
        >
          <span>Filters</span>
          <svg
            className={`w-5 h-5 transform transition-transform ${
              isExpanded ? "rotate-180" : ""
            }`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M19 9l-7 7-7-7"
            />
          </svg>
        </button>
      </div>

      <div className={`space-y-6 ${!isExpanded ? "hidden lg:block" : ""}`}>
        {/* Clear Filters */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">Filters</h3>
          <button
            onClick={clearFilters}
            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
          >
            Clear All
          </button>
        </div>

        {/* Price Filter */}
        <div>
          <h4 className="font-medium text-gray-900 mb-3">Price</h4>
          <div className="space-y-2">
            <label className="flex items-center">
              <input
                type="radio"
                name="price"
                checked={localFilters.isPaid === undefined}
                onChange={() => handleFilterChange("isPaid", undefined)}
                className="mr-2 text-blue-600"
              />
              <span className="text-sm text-gray-700">All</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="price"
                checked={localFilters.isPaid === false}
                onChange={() => handleFilterChange("isPaid", false)}
                className="mr-2 text-blue-600"
              />
              <span className="text-sm text-gray-700">Free</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="price"
                checked={localFilters.isPaid === true}
                onChange={() => handleFilterChange("isPaid", true)}
                className="mr-2 text-blue-600"
              />
              <span className="text-sm text-gray-700">Paid</span>
            </label>
          </div>
        </div>

        {/* Level Filter */}
        <div>
          <h4 className="font-medium text-gray-900 mb-3">Level</h4>
          <div className="space-y-2">
            <label className="flex items-center">
              <input
                type="radio"
                name="level"
                checked={!localFilters.level}
                onChange={() => handleFilterChange("level", "")}
                className="mr-2 text-blue-600"
              />
              <span className="text-sm text-gray-700">All Levels</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="level"
                checked={localFilters.level === "BEGINNER"}
                onChange={() => handleFilterChange("level", "BEGINNER")}
                className="mr-2 text-blue-600"
              />
              <span className="text-sm text-gray-700">Beginner</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="level"
                checked={localFilters.level === "INTERMEDIATE"}
                onChange={() => handleFilterChange("level", "INTERMEDIATE")}
                className="mr-2 text-blue-600"
              />
              <span className="text-sm text-gray-700">Intermediate</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="level"
                checked={localFilters.level === "ADVANCED"}
                onChange={() => handleFilterChange("level", "ADVANCED")}
                className="mr-2 text-blue-600"
              />
              <span className="text-sm text-gray-700">Advanced</span>
            </label>
          </div>
        </div>

        {/* Category Filter */}
        <div>
          <h4 className="font-medium text-gray-900 mb-3">Category</h4>
          <select
            value={localFilters.category || ""}
            onChange={(e) => handleFilterChange("category", e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Categories</option>
            {categories.map((category) => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>
        </div>

        {/* Sector Filter */}
        {sectors && sectors.length > 0 && (
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Sector</h4>
            <select
              value={localFilters.sectorId || ""}
              onChange={(e) => handleFilterChange("sectorId", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Sectors</option>
              {sectors.map((sector: any) => (
                <option key={sector._id} value={sector._id}>
                  {sector.name}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Sub-Sector Filter */}
        {localFilters.sectorId && subSectors && subSectors.length > 0 && (
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Sub-Sector</h4>
            <select
              value={localFilters.subSectorId || ""}
              onChange={(e) =>
                handleFilterChange("subSectorId", e.target.value)
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Sub-Sectors</option>
              {subSectors
                .filter(
                  (subSector: any) =>
                    subSector.sectorId === localFilters.sectorId
                )
                .map((subSector: any) => (
                  <option key={subSector._id} value={subSector._id}>
                    {subSector.name}
                  </option>
                ))}
            </select>
          </div>
        )}

        {/* Active Filters Display */}
        {Object.keys(localFilters).some(
          (key) => localFilters[key as keyof FilterType]
        ) && (
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Active Filters</h4>
            <div className="flex flex-wrap gap-2">
              {localFilters.isPaid !== undefined && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {localFilters.isPaid ? "Paid" : "Free"}
                  <button
                    onClick={() => handleFilterChange("isPaid", undefined)}
                    className="ml-2 text-blue-600 hover:text-blue-800"
                  >
                    ×
                  </button>
                </span>
              )}
              {localFilters.level && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {localFilters.level}
                  <button
                    onClick={() => handleFilterChange("level", "")}
                    className="ml-2 text-blue-600 hover:text-blue-800"
                  >
                    ×
                  </button>
                </span>
              )}
              {localFilters.category && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {localFilters.category}
                  <button
                    onClick={() => handleFilterChange("category", "")}
                    className="ml-2 text-blue-600 hover:text-blue-800"
                  >
                    ×
                  </button>
                </span>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MasterclassFilters;
