import React from "react";
import { MasterclassItem } from "../../types/masterclass";

interface MasterclassCardProps {
  masterclass: MasterclassItem;
  onClick: () => void;
}

const MasterclassCard: React.FC<MasterclassCardProps> = ({
  masterclass,
  onClick,
}) => {
  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case "BEGINNER":
        return "bg-gradient-to-r from-green-400 to-green-500 text-white shadow-green-200";
      case "INTERMEDIATE":
        return "bg-gradient-to-r from-yellow-400 to-orange-500 text-white shadow-yellow-200";
      case "ADVANCED":
        return "bg-gradient-to-r from-red-400 to-red-500 text-white shadow-red-200";
      default:
        return "bg-gradient-to-r from-gray-400 to-gray-500 text-white shadow-gray-200";
    }
  };

  const getPriceGradient = () => {
    return masterclass.isPaid
      ? "bg-gradient-to-r from-blue-500 to-blue-600 shadow-lg shadow-blue-200"
      : "bg-gradient-to-r from-green-500 to-green-600 shadow-lg shadow-green-200";
  };

  return (
    <div
      className="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 cursor-pointer transform hover:-translate-y-2 hover:scale-[1.02] overflow-hidden border border-gray-100 hover:border-blue-200"
      onClick={onClick}
    >
      {/* Thumbnail */}
      <div className="relative overflow-hidden">
        <img
          src={masterclass.thumbnailImage}
          alt={masterclass.title}
          className="w-full h-52 object-cover transition-transform duration-500 group-hover:scale-110"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = "/api/placeholder/400/300";
          }}
        />

        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        {/* Price Badge */}
        <div className="absolute top-4 right-4">
          <span
            className={`${getPriceGradient()} text-white px-4 py-2 rounded-full text-sm font-bold backdrop-blur-sm`}
          >
            {masterclass.isPaid ? `$${masterclass.price}` : "FREE"}
          </span>
        </div>

        {/* Duration Badge */}
        <div className="absolute bottom-4 right-4 bg-black/80 backdrop-blur-sm text-white px-3 py-1.5 rounded-lg text-sm font-medium">
          <svg
            className="w-4 h-4 inline mr-1"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
              clipRule="evenodd"
            />
          </svg>
          {formatDuration(masterclass.duration)}
        </div>

        {/* Play Button Overlay */}
        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div className="bg-white/90 backdrop-blur-sm rounded-full p-4 transform scale-75 group-hover:scale-100 transition-transform duration-300">
            <svg
              className="w-8 h-8 text-blue-600"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path d="M6.3 2.841A1.5 1.5 0 004 4.11V15.89a1.5 1.5 0 002.3 1.269l9.344-5.89a1.5 1.5 0 000-2.538L6.3 2.84z" />
            </svg>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Level Badge */}
        <div className="mb-4">
          <span
            className={`inline-flex items-center px-3 py-1.5 rounded-full text-xs font-bold shadow-lg ${getLevelColor(
              masterclass.level
            )}`}
          >
            <svg
              className="w-3 h-3 mr-1"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
            {masterclass.level}
          </span>
        </div>

        {/* Title */}
        <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2 group-hover:text-blue-600 transition-colors duration-300">
          {masterclass.title}
        </h3>

        {/* Instructor */}
        <div className="flex items-center mb-4">
          {masterclass.instructorImage ? (
            <img
              src={masterclass.instructorImage}
              alt={masterclass.instructor}
              className="w-10 h-10 rounded-full mr-3 ring-2 ring-gray-100"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = "/api/placeholder/32/32";
              }}
            />
          ) : (
            <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full mr-3 flex items-center justify-center ring-2 ring-gray-100">
              <span className="text-white text-sm font-bold">
                {masterclass.instructor.charAt(0).toUpperCase()}
              </span>
            </div>
          )}
          <div>
            <span className="text-gray-800 text-sm font-semibold block">
              {masterclass.instructor}
            </span>
            <span className="text-gray-500 text-xs">Instructor</span>
          </div>
        </div>

        {/* Short Description */}
        <p className="text-gray-600 text-sm mb-5 line-clamp-2 leading-relaxed">
          {masterclass.shortDescription}
        </p>

        {/* Stats */}
        <div className="flex items-center justify-between mb-5">
          <div className="flex items-center bg-yellow-50 px-3 py-1.5 rounded-lg">
            <svg
              className="w-4 h-4 mr-1 text-yellow-500"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
            <span className="text-gray-800 font-semibold text-sm">
              {masterclass.rating ? masterclass.rating.toFixed(1) : "N/A"}
            </span>
            <span className="text-gray-500 text-xs ml-1">
              ({masterclass.reviewCount || 0})
            </span>
          </div>

          <div className="flex items-center bg-blue-50 px-3 py-1.5 rounded-lg">
            <svg
              className="w-4 h-4 mr-1 text-blue-500"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
            </svg>
            <span className="text-gray-800 font-semibold text-sm">
              {masterclass.enrollmentCount || 0}
            </span>
            <span className="text-gray-500 text-xs ml-1">enrolled</span>
          </div>
        </div>

        {/* Tags */}
        {masterclass.tags && masterclass.tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-5">
            {masterclass.tags.slice(0, 2).map((tag, index) => (
              <span
                key={index}
                className="bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 px-3 py-1 rounded-full text-xs font-medium border border-gray-200 hover:shadow-sm transition-shadow duration-200"
              >
                #{tag}
              </span>
            ))}
            {masterclass.tags.length > 2 && (
              <span className="text-gray-400 text-xs font-medium px-2 py-1">
                +{masterclass.tags.length - 2} more
              </span>
            )}
          </div>
        )}

        {/* Action Button */}
        <button className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white py-3.5 rounded-xl font-bold text-sm shadow-lg shadow-blue-200 hover:shadow-xl hover:shadow-blue-300 transition-all duration-300 transform hover:scale-[1.02] flex items-center justify-center group">
          <span>View Details</span>
          <svg
            className="w-4 h-4 ml-2 transform group-hover:translate-x-1 transition-transform duration-200"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5l7 7-7 7"
            />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default MasterclassCard;
