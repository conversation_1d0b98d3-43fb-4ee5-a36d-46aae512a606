import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet";
import { createNewMasterclass } from "../../store/actioncreators/masterclassactions";
import { MasterclassCreate } from "../../types/masterclass";
import LoadingSpinner from "../Loader/LoadingSpinner";

interface CreateMasterclassProps {
  handleLoginModal: () => void;
}

const CreateMasterclass: React.FC<CreateMasterclassProps> = ({
  handleLoginModal,
}) => {
  const dispatch: any = useDispatch();
  const navigate = useNavigate();

  const { LOADING } = useSelector((state: any) => state.MASTERCLASS);
  const currentUser = useSelector((state: any) => state.USER.USER);

  const [formData, setFormData] = useState<MasterclassCreate>({
    title: "",
    description: "",
    shortDescription: "",
    instructor: "",
    instructorBio: "",
    instructorImage: "",
    duration: 60,
    level: "BEGINNER",
    category: "",
    tags: [],
    thumbnailImage: "",
    videoUrl: "",
    price: 0,
    currency: "USD",
    isPaid: false,
    prerequisites: [],
    learningOutcomes: [],
    resources: [],
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [tagInput, setTagInput] = useState("");
  const [prerequisiteInput, setPrerequisiteInput] = useState("");
  const [outcomeInput, setOutcomeInput] = useState("");

  // Check if user is admin
  if (currentUser.admin === -1) {
    handleLoginModal();
    return null;
  }

  if (currentUser.admin !== 0) {
    // Assuming 0 is super admin
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Access Denied
          </h2>
          <p className="text-gray-600 mb-6">
            You don't have permission to create masterclasses.
          </p>
          <button
            onClick={() => navigate("/masterclass")}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Masterclasses
          </button>
        </div>
      </div>
    );
  }

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value, type } = e.target;

    if (type === "checkbox") {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData((prev) => ({
        ...prev,
        [name]: checked,
      }));
    } else if (name === "duration" || name === "price") {
      setFormData((prev) => ({
        ...prev,
        [name]: parseFloat(value) || 0,
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const addTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData((prev) => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()],
      }));
      setTagInput("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData((prev) => ({
      ...prev,
      tags: prev.tags.filter((tag) => tag !== tagToRemove),
    }));
  };

  const addPrerequisite = () => {
    if (
      prerequisiteInput.trim() &&
      !formData.prerequisites?.includes(prerequisiteInput.trim())
    ) {
      setFormData((prev) => ({
        ...prev,
        prerequisites: [
          ...(prev.prerequisites || []),
          prerequisiteInput.trim(),
        ],
      }));
      setPrerequisiteInput("");
    }
  };

  const removePrerequisite = (prereqToRemove: string) => {
    setFormData((prev) => ({
      ...prev,
      prerequisites:
        prev.prerequisites?.filter((prereq) => prereq !== prereqToRemove) || [],
    }));
  };

  const addOutcome = () => {
    if (
      outcomeInput.trim() &&
      !formData.learningOutcomes?.includes(outcomeInput.trim())
    ) {
      setFormData((prev) => ({
        ...prev,
        learningOutcomes: [
          ...(prev.learningOutcomes || []),
          outcomeInput.trim(),
        ],
      }));
      setOutcomeInput("");
    }
  };

  const removeOutcome = (outcomeToRemove: string) => {
    setFormData((prev) => ({
      ...prev,
      learningOutcomes:
        prev.learningOutcomes?.filter(
          (outcome) => outcome !== outcomeToRemove
        ) || [],
    }));
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) newErrors.title = "Title is required";
    if (!formData.description.trim())
      newErrors.description = "Description is required";
    if (!formData.shortDescription.trim())
      newErrors.shortDescription = "Short description is required";
    if (!formData.instructor.trim())
      newErrors.instructor = "Instructor name is required";
    if (!formData.category.trim()) newErrors.category = "Category is required";
    if (!formData.thumbnailImage.trim())
      newErrors.thumbnailImage = "Thumbnail image URL is required";
    if (!formData.videoUrl.trim()) newErrors.videoUrl = "Video URL is required";
    if (formData.duration <= 0)
      newErrors.duration = "Duration must be greater than 0";
    if (formData.isPaid && formData.price <= 0)
      newErrors.price = "Price must be greater than 0 for paid courses";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    const result = await dispatch(createNewMasterclass(formData));

    if (result.success) {
      navigate("/masterclass");
    }
  };

  return (
    <>
      <Helmet>
        <title>Create Masterclass | GTI Admin</title>
        <meta name="description" content="Create a new masterclass" />
      </Helmet>

      <div className="min-h-screen bg-gray-50 py-12">
        <div className="w-full max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900">
                Create New Masterclass
              </h1>
              <p className="text-gray-600 mt-2">
                Fill in the details to create a new masterclass
              </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Title *
                  </label>
                  <input
                    type="text"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.title ? "border-red-500" : "border-gray-300"
                    }`}
                    placeholder="Enter masterclass title"
                  />
                  {errors.title && (
                    <p className="text-red-500 text-sm mt-1">{errors.title}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Category *
                  </label>
                  <input
                    type="text"
                    name="category"
                    value={formData.category}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.category ? "border-red-500" : "border-gray-300"
                    }`}
                    placeholder="e.g., Technology, Business, Design"
                  />
                  {errors.category && (
                    <p className="text-red-500 text-sm mt-1">
                      {errors.category}
                    </p>
                  )}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Short Description *
                </label>
                <textarea
                  name="shortDescription"
                  value={formData.shortDescription}
                  onChange={handleInputChange}
                  rows={2}
                  className={`w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.shortDescription
                      ? "border-red-500"
                      : "border-gray-300"
                  }`}
                  placeholder="Brief description for the card view"
                />
                {errors.shortDescription && (
                  <p className="text-red-500 text-sm mt-1">
                    {errors.shortDescription}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Full Description *
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={6}
                  className={`w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.description ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder="Detailed description of the masterclass"
                />
                {errors.description && (
                  <p className="text-red-500 text-sm mt-1">
                    {errors.description}
                  </p>
                )}
              </div>

              {/* Instructor Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Instructor Name *
                  </label>
                  <input
                    type="text"
                    name="instructor"
                    value={formData.instructor}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.instructor ? "border-red-500" : "border-gray-300"
                    }`}
                    placeholder="Instructor's full name"
                  />
                  {errors.instructor && (
                    <p className="text-red-500 text-sm mt-1">
                      {errors.instructor}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Instructor Image URL
                  </label>
                  <input
                    type="url"
                    name="instructorImage"
                    value={formData.instructorImage}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="https://example.com/instructor-photo.jpg"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Instructor Bio
                </label>
                <textarea
                  name="instructorBio"
                  value={formData.instructorBio}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Brief bio about the instructor"
                />
              </div>

              {/* Course Details */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Duration (minutes) *
                  </label>
                  <input
                    type="number"
                    name="duration"
                    value={formData.duration}
                    onChange={handleInputChange}
                    min="1"
                    className={`w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.duration ? "border-red-500" : "border-gray-300"
                    }`}
                  />
                  {errors.duration && (
                    <p className="text-red-500 text-sm mt-1">
                      {errors.duration}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Level *
                  </label>
                  <select
                    name="level"
                    value={formData.level}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="BEGINNER">Beginner</option>
                    <option value="INTERMEDIATE">Intermediate</option>
                    <option value="ADVANCED">Advanced</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <input
                      type="checkbox"
                      name="isPaid"
                      checked={formData.isPaid}
                      onChange={handleInputChange}
                      className="mr-2"
                    />
                    Paid Course
                  </label>
                  {formData.isPaid && (
                    <input
                      type="number"
                      name="price"
                      value={formData.price}
                      onChange={handleInputChange}
                      min="0"
                      step="0.01"
                      className={`w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        errors.price ? "border-red-500" : "border-gray-300"
                      }`}
                      placeholder="Price in USD"
                    />
                  )}
                  {errors.price && (
                    <p className="text-red-500 text-sm mt-1">{errors.price}</p>
                  )}
                </div>
              </div>

              {/* Media URLs */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Thumbnail Image URL *
                  </label>
                  <input
                    type="url"
                    name="thumbnailImage"
                    value={formData.thumbnailImage}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.thumbnailImage
                        ? "border-red-500"
                        : "border-gray-300"
                    }`}
                    placeholder="https://example.com/thumbnail.jpg"
                  />
                  {errors.thumbnailImage && (
                    <p className="text-red-500 text-sm mt-1">
                      {errors.thumbnailImage}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Video URL *
                  </label>
                  <input
                    type="url"
                    name="videoUrl"
                    value={formData.videoUrl}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.videoUrl ? "border-red-500" : "border-gray-300"
                    }`}
                    placeholder="https://example.com/video.mp4"
                  />
                  {errors.videoUrl && (
                    <p className="text-red-500 text-sm mt-1">
                      {errors.videoUrl}
                    </p>
                  )}
                </div>
              </div>

              {/* Tags */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tags
                </label>
                <div className="flex gap-2 mb-2">
                  <input
                    type="text"
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    onKeyPress={(e) =>
                      e.key === "Enter" && (e.preventDefault(), addTag())
                    }
                    className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Add a tag and press Enter"
                  />
                  <button
                    type="button"
                    onClick={addTag}
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Add
                  </button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {formData.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm flex items-center gap-2"
                    >
                      {tag}
                      <button
                        type="button"
                        onClick={() => removeTag(tag)}
                        className="text-blue-600 hover:text-blue-800"
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => navigate("/masterclass")}
                  className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={LOADING}
                  className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                >
                  {LOADING && (
                    <LoadingSpinner size="small" color="text-white" />
                  )}
                  Create Masterclass
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </>
  );
};

export default CreateMasterclass;
