import React, { useEffect, useState, useRef } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { Helmet } from "react-helmet";
import {
  getMasterclassDetails,
  clearCurrentMasterclass,
} from "../../store/actioncreators/masterclassactions";
import {
  checkMasterclassAccess,
  updateMasterclassProgress,
} from "../../api/masterclass";
import LoadingSpinner from "../Loader/LoadingSpinner";

interface MasterclassVideoProps {
  handleLoginModal: () => void;
}

const MasterclassVideo: React.FC<MasterclassVideoProps> = ({
  handleLoginModal,
}) => {
  const { id } = useParams<{ id: string }>();
  const dispatch: any = useDispatch();
  const navigate = useNavigate();
  const videoRef = useRef<HTMLVideoElement>(null);

  const { CURRENT_MASTERCLASS, LOADING, ERROR } = useSelector(
    (state: any) => state.MASTERCLASS
  );
  const currentUser = useSelector((state: any) => state.USER.USER);

  const [hasAccess, setHasAccess] = useState(false);
  const [isCheckingAccess, setIsCheckingAccess] = useState(true);
  const [progress, setProgress] = useState(0);
  const [isCompleted, setIsCompleted] = useState(false);
  const [showControls, setShowControls] = useState(true);

  useEffect(() => {
    if (currentUser.admin === -1) {
      handleLoginModal();
      return;
    }

    if (id) {
      dispatch(getMasterclassDetails(id));
      checkAccess();
    }

    return () => {
      dispatch(clearCurrentMasterclass());
    };
  }, [id, dispatch, currentUser.admin]);

  const checkAccess = async () => {
    if (!id) return;

    setIsCheckingAccess(true);
    try {
      const response = await checkMasterclassAccess(id);
      if (response?.status === 200) {
        setHasAccess(response.data.hasAccess);
        setProgress(response.data.progress || 0);
        setIsCompleted(response.data.isCompleted || false);
      } else {
        setHasAccess(false);
      }
    } catch (error) {
      console.error("Error checking access:", error);
      setHasAccess(false);
    } finally {
      setIsCheckingAccess(false);
    }
  };

  const handleTimeUpdate = () => {
    if (!videoRef.current || !id) return;

    const video = videoRef.current;
    const currentProgress = (video.currentTime / video.duration) * 100;

    if (currentProgress > progress) {
      setProgress(currentProgress);

      // Update progress on server every 10% increment
      if (Math.floor(currentProgress / 10) > Math.floor(progress / 10)) {
        updateProgress(currentProgress);
      }
    }

    // Mark as completed when 90% watched
    if (currentProgress >= 90 && !isCompleted) {
      setIsCompleted(true);
      updateProgress(100);
    }
  };

  const updateProgress = async (newProgress: number) => {
    if (!id) return;

    try {
      await updateMasterclassProgress(id, newProgress);
    } catch (error) {
      console.error("Error updating progress:", error);
    }
  };

  const handleVideoEnd = () => {
    if (!isCompleted) {
      setIsCompleted(true);
      setProgress(100);
      updateProgress(100);
    }
  };

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, "0")}:${secs
        .toString()
        .padStart(2, "0")}`;
    }
    return `${minutes}:${secs.toString().padStart(2, "0")}`;
  };

  if (isCheckingAccess || LOADING) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-black">
        <LoadingSpinner />
      </div>
    );
  }

  if (ERROR || !CURRENT_MASTERCLASS) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-black text-white">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">
            {ERROR || "Masterclass not found"}
          </h2>
          <button
            onClick={() => navigate("/masterclass")}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Masterclasses
          </button>
        </div>
      </div>
    );
  }

  if (!hasAccess) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-black text-white">
        <div className="text-center max-w-md">
          <div className="w-16 h-16 mx-auto mb-6 bg-red-100 rounded-full flex items-center justify-center">
            <svg
              className="w-8 h-8 text-red-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
              />
            </svg>
          </div>

          <h2 className="text-2xl font-bold mb-4">Access Required</h2>
          <p className="text-gray-300 mb-8">
            You need to enroll in this masterclass to access the video content.
          </p>

          <div className="space-y-4">
            <button
              onClick={() => navigate(`/masterclass/${id}`)}
              className="w-full bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
            >
              View Masterclass Details
            </button>
            <button
              onClick={() => navigate("/masterclass")}
              className="w-full border border-gray-600 text-gray-300 px-6 py-3 rounded-lg font-semibold hover:bg-gray-800 transition-colors"
            >
              Browse All Masterclasses
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>{CURRENT_MASTERCLASS.title} - Video | GTI Masterclass</title>
        <meta
          name="description"
          content={`Watch ${CURRENT_MASTERCLASS.title} masterclass video`}
        />
      </Helmet>

      <div className="min-h-screen bg-black text-white">
        {/* Video Player */}
        <div className="relative">
          <video
            ref={videoRef}
            className="w-full h-screen object-contain"
            controls={showControls}
            controlsList="nodownload"
            onTimeUpdate={handleTimeUpdate}
            onEnded={handleVideoEnd}
            onMouseEnter={() => setShowControls(true)}
            onMouseLeave={() => setShowControls(false)}
          >
            <source src={CURRENT_MASTERCLASS.videoUrl} type="video/mp4" />
            Your browser does not support the video tag.
          </video>

          {/* Custom Overlay Controls */}
          <div
            className={`absolute top-0 left-0 right-0 bg-gradient-to-b from-black/70 to-transparent p-6 transition-opacity duration-300 ${
              showControls ? "opacity-100" : "opacity-0"
            }`}
          >
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold mb-2">
                  {CURRENT_MASTERCLASS.title}
                </h1>
                <p className="text-gray-300">
                  Instructor: {CURRENT_MASTERCLASS.instructor}
                </p>
              </div>

              <div className="flex items-center space-x-4">
                <button
                  onClick={() => navigate(`/masterclass/${id}`)}
                  className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  Course Details
                </button>
                <button
                  onClick={() => navigate("/masterclass")}
                  className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  All Masterclasses
                </button>
              </div>
            </div>
          </div>

          {/* Progress Bar */}
          <div
            className={`absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-6 transition-opacity duration-300 ${
              showControls ? "opacity-100" : "opacity-0"
            }`}
          >
            <div className="mb-4">
              <div className="flex items-center justify-between text-sm text-gray-300 mb-2">
                <span>Progress: {Math.round(progress)}%</span>
                <span>{isCompleted ? "Completed" : "In Progress"}</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                />
              </div>
            </div>
          </div>

          {/* Completion Badge */}
          {isCompleted && (
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-green-600 text-white px-6 py-3 rounded-lg font-semibold animate-pulse">
              🎉 Masterclass Completed!
            </div>
          )}
        </div>

        {/* Mobile-friendly bottom info */}
        <div className="lg:hidden p-4 bg-gray-900">
          <h2 className="text-lg font-semibold mb-2">
            {CURRENT_MASTERCLASS.title}
          </h2>
          <p className="text-gray-400 text-sm mb-3">
            by {CURRENT_MASTERCLASS.instructor}
          </p>

          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-400">
              Progress: {Math.round(progress)}%
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => navigate(`/masterclass/${id}`)}
                className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors"
              >
                Details
              </button>
              <button
                onClick={() => navigate("/masterclass")}
                className="bg-gray-600 text-white px-3 py-1 rounded text-sm hover:bg-gray-700 transition-colors"
              >
                Browse
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default MasterclassVideo;
