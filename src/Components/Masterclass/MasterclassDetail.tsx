import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { Helmet } from "react-helmet";
import {
  getMasterclassDetails,
  enrollUserInMasterclass,
  clearCurrentMasterclass,
} from "../../store/actioncreators/masterclassactions";
import {
  createMasterclassPayment,
  checkMasterclassAccess,
} from "../../api/masterclass";
import { Company } from "../../shared/constants";

import LoadingSpinner from "../Loader/LoadingSpinner";
import gbi_home_logo from "../../assests/home/<USER>";

interface MasterclassDetailProps {
  handleLoginModal: () => void;
}

const MasterclassDetail: React.FC<MasterclassDetailProps> = ({
  handleLoginModal,
}) => {
  const { id } = useParams<{ id: string }>();
  const dispatch: any = useDispatch();
  const navigate = useNavigate();

  const { CURRENT_MASTERCLASS, LOADING, ERROR } = useSelector(
    (state: any) => state.MASTERCLASS
  );
  const currentUser = useSelector((state: any) => state.USER.USER);

  const [hasAccess, setHasAccess] = useState(false);
  const [isEnrolling, setIsEnrolling] = useState(false);
  const [enrollmentError, setEnrollmentError] = useState("");
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);

  useEffect(() => {
    if (id) {
      dispatch(getMasterclassDetails(id));

      // Check if user has access to this masterclass
      if (currentUser.admin !== -1) {
        checkAccess();
      }
    }

    return () => {
      dispatch(clearCurrentMasterclass());
    };
  }, [id, dispatch, currentUser.admin]);

  const checkAccess = async () => {
    if (!id) return;

    try {
      const response = await checkMasterclassAccess(id);
      if (response?.status === 200) {
        setHasAccess(response.data.hasAccess);
      }
    } catch (error) {
      console.error("Error checking access:", error);
    }
  };

  const handleEnrollment = async () => {
    if (currentUser.admin === -1) {
      handleLoginModal();
      return;
    }

    if (!CURRENT_MASTERCLASS || !id) return;

    if (CURRENT_MASTERCLASS.isPaid) {
      handlePaidEnrollment();
    } else {
      handleFreeEnrollment();
    }
  };

  const handleFreeEnrollment = async () => {
    if (!id) return;

    setIsEnrolling(true);
    setEnrollmentError("");

    try {
      const result = await dispatch(enrollUserInMasterclass(id));
      if (result.success) {
        setHasAccess(true);
        navigate(`/masterclass-video/${id}`);
      } else {
        setEnrollmentError(result.error || "Failed to enroll in masterclass");
      }
    } catch (error) {
      setEnrollmentError("An error occurred during enrollment");
    } finally {
      setIsEnrolling(false);
    }
  };

  const handlePaidEnrollment = async () => {
    if (!id || !CURRENT_MASTERCLASS) return;

    setIsProcessingPayment(true);
    setEnrollmentError("");

    try {
      const paymentData = await createMasterclassPayment(id);

      // Initialize Razorpay payment
      const options = {
        key: process.env.REACT_APP_RAZORPAY_KEY_ID,
        amount: paymentData.amount,
        currency: paymentData.currency,
        name: Company.NAME,
        description: Company.DESCRIPTION,
        image: gbi_home_logo,
        order_id: paymentData.order_id,
        callback_url: `${process.env.REACT_APP_BASE_API}/payments/validate`,
        prefill: {
          name: currentUser?.user?.name,
          email: currentUser?.user?.email,
        },
        notes: {
          masterclassId: id,
        },
        theme: {
          color: "#3399cc",
        },
      };

      const rzp = new (window as any).Razorpay(options);

      rzp.on("payment.failed", function (response: any) {
        setEnrollmentError("Payment failed. Please try again.");
        setIsProcessingPayment(false);
      });

      rzp.on("payment.success", function (response: any) {
        setHasAccess(true);
        setIsProcessingPayment(false);
        navigate(`/masterclass-video/${id}`);
      });

      rzp.open();
    } catch (error) {
      setEnrollmentError("Failed to initiate payment. Please try again.");
      setIsProcessingPayment(false);
    }
  };

  const handleWatchNow = () => {
    if (hasAccess && id) {
      navigate(`/masterclass-video/${id}`);
    }
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case "BEGINNER":
        return "bg-green-100 text-green-800";
      case "INTERMEDIATE":
        return "bg-yellow-100 text-yellow-800";
      case "ADVANCED":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (LOADING) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  if (ERROR || !CURRENT_MASTERCLASS) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            {ERROR || "Masterclass not found"}
          </h2>
          <button
            onClick={() => navigate("/masterclass")}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Masterclasses
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>{CURRENT_MASTERCLASS.title} | GTI Masterclass</title>
        <meta
          name="description"
          content={CURRENT_MASTERCLASS.shortDescription}
        />
      </Helmet>

      <div className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white">
          <div className="w-full px-4 sm:px-6 lg:px-8 py-16">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              {/* Content */}
              <div>
                <div className="mb-4">
                  <span
                    className={`inline-block px-3 py-1 rounded-full text-sm font-semibold ${getLevelColor(
                      CURRENT_MASTERCLASS.level
                    )}`}
                  >
                    {CURRENT_MASTERCLASS.level}
                  </span>
                </div>

                <h1 className="text-4xl md:text-5xl font-bold mb-6">
                  {CURRENT_MASTERCLASS.title}
                </h1>

                <p className="text-xl text-blue-100 mb-6">
                  {CURRENT_MASTERCLASS.shortDescription}
                </p>

                {/* Instructor Info */}
                <div className="flex items-center mb-6">
                  {CURRENT_MASTERCLASS.instructorImage ? (
                    <img
                      src={CURRENT_MASTERCLASS.instructorImage}
                      alt={CURRENT_MASTERCLASS.instructor}
                      className="w-12 h-12 rounded-full mr-4"
                    />
                  ) : (
                    <div className="w-12 h-12 bg-blue-500 rounded-full mr-4 flex items-center justify-center">
                      <span className="text-white font-semibold">
                        {CURRENT_MASTERCLASS.instructor.charAt(0).toUpperCase()}
                      </span>
                    </div>
                  )}
                  <div>
                    <p className="font-semibold">Instructor</p>
                    <p className="text-blue-100">
                      {CURRENT_MASTERCLASS.instructor}
                    </p>
                  </div>
                </div>

                {/* Stats */}
                <div className="flex items-center space-x-6 mb-8 text-blue-100">
                  <div className="flex items-center">
                    <svg
                      className="w-5 h-5 mr-2"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                      <path
                        fillRule="evenodd"
                        d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span>
                      {CURRENT_MASTERCLASS.enrollmentCount || 0} enrolled
                    </span>
                  </div>
                  <div className="flex items-center">
                    <svg
                      className="w-5 h-5 mr-2"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" />
                    </svg>
                    <span>{formatDuration(CURRENT_MASTERCLASS.duration)}</span>
                  </div>
                  {CURRENT_MASTERCLASS.rating && (
                    <div className="flex items-center">
                      <svg
                        className="w-5 h-5 mr-2"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                      <span>
                        {CURRENT_MASTERCLASS.rating.toFixed(1)} (
                        {CURRENT_MASTERCLASS.reviewCount || 0} reviews)
                      </span>
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-4">
                  {hasAccess ? (
                    <button
                      onClick={handleWatchNow}
                      className="bg-green-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors"
                    >
                      Watch Now
                    </button>
                  ) : (
                    <button
                      onClick={handleEnrollment}
                      disabled={isEnrolling || isProcessingPayment}
                      className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isEnrolling || isProcessingPayment
                        ? "Processing..."
                        : CURRENT_MASTERCLASS.isPaid
                        ? `Enroll for $${CURRENT_MASTERCLASS.price}`
                        : "Enroll for Free"}
                    </button>
                  )}

                  <button
                    onClick={() => navigate("/masterclass")}
                    className="border border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors"
                  >
                    Back to Masterclasses
                  </button>
                </div>

                {/* Error Message */}
                {enrollmentError && (
                  <div className="mt-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
                    {enrollmentError}
                  </div>
                )}
              </div>

              {/* Thumbnail */}
              <div className="relative">
                <img
                  src={CURRENT_MASTERCLASS.thumbnailImage}
                  alt={CURRENT_MASTERCLASS.title}
                  className="w-full rounded-xl shadow-2xl"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = "/api/placeholder/600/400";
                  }}
                />

                {/* Price Badge */}
                <div className="absolute top-4 right-4">
                  {CURRENT_MASTERCLASS.isPaid ? (
                    <span className="bg-blue-600 text-white px-4 py-2 rounded-full text-lg font-semibold">
                      ${CURRENT_MASTERCLASS.price}
                    </span>
                  ) : (
                    <span className="bg-green-600 text-white px-4 py-2 rounded-full text-lg font-semibold">
                      FREE
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Content Sections */}
        <div className="w-full px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-8">
              {/* Description */}
              <div className="bg-white rounded-xl shadow-lg p-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-6">
                  About This Masterclass
                </h2>
                <div
                  className="prose prose-lg max-w-none text-gray-700"
                  dangerouslySetInnerHTML={{
                    __html: CURRENT_MASTERCLASS.description,
                  }}
                />
              </div>

              {/* Learning Outcomes */}
              {CURRENT_MASTERCLASS.learningOutcomes &&
                CURRENT_MASTERCLASS.learningOutcomes.length > 0 && (
                  <div className="bg-white rounded-xl shadow-lg p-8">
                    <h2 className="text-2xl font-bold text-gray-900 mb-6">
                      What You'll Learn
                    </h2>
                    <ul className="space-y-3">
                      {CURRENT_MASTERCLASS.learningOutcomes.map(
                        (outcome: string, index: number) => (
                          <li key={index} className="flex items-start">
                            <svg
                              className="w-6 h-6 text-green-500 mr-3 mt-0.5 flex-shrink-0"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clipRule="evenodd"
                              />
                            </svg>
                            <span className="text-gray-700">{outcome}</span>
                          </li>
                        )
                      )}
                    </ul>
                  </div>
                )}

              {/* Prerequisites */}
              {CURRENT_MASTERCLASS.prerequisites &&
                CURRENT_MASTERCLASS.prerequisites.length > 0 && (
                  <div className="bg-white rounded-xl shadow-lg p-8">
                    <h2 className="text-2xl font-bold text-gray-900 mb-6">
                      Prerequisites
                    </h2>
                    <ul className="space-y-3">
                      {CURRENT_MASTERCLASS.prerequisites.map(
                        (prerequisite: string, index: number) => (
                          <li key={index} className="flex items-start">
                            <svg
                              className="w-6 h-6 text-blue-500 mr-3 mt-0.5 flex-shrink-0"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                clipRule="evenodd"
                              />
                            </svg>
                            <span className="text-gray-700">
                              {prerequisite}
                            </span>
                          </li>
                        )
                      )}
                    </ul>
                  </div>
                )}

              {/* Instructor Bio */}
              {CURRENT_MASTERCLASS.instructorBio && (
                <div className="bg-white rounded-xl shadow-lg p-8">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">
                    About the Instructor
                  </h2>
                  <div className="flex items-start space-x-6">
                    {CURRENT_MASTERCLASS.instructorImage ? (
                      <img
                        src={CURRENT_MASTERCLASS.instructorImage}
                        alt={CURRENT_MASTERCLASS.instructor}
                        className="w-20 h-20 rounded-full flex-shrink-0"
                      />
                    ) : (
                      <div className="w-20 h-20 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                        <span className="text-white text-2xl font-semibold">
                          {CURRENT_MASTERCLASS.instructor
                            .charAt(0)
                            .toUpperCase()}
                        </span>
                      </div>
                    )}
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        {CURRENT_MASTERCLASS.instructor}
                      </h3>
                      <p className="text-gray-700">
                        {CURRENT_MASTERCLASS.instructorBio}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Course Info Card */}
              <div className="bg-white rounded-xl shadow-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Course Information
                </h3>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Duration</span>
                    <span className="font-medium">
                      {formatDuration(CURRENT_MASTERCLASS.duration)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Level</span>
                    <span className="font-medium">
                      {CURRENT_MASTERCLASS.level}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Category</span>
                    <span className="font-medium">
                      {CURRENT_MASTERCLASS.category}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Students</span>
                    <span className="font-medium">
                      {CURRENT_MASTERCLASS.enrollmentCount || 0}
                    </span>
                  </div>
                  {CURRENT_MASTERCLASS.rating && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Rating</span>
                      <span className="font-medium">
                        {CURRENT_MASTERCLASS.rating.toFixed(1)} ⭐ (
                        {CURRENT_MASTERCLASS.reviewCount || 0})
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Tags */}
              {CURRENT_MASTERCLASS.tags &&
                CURRENT_MASTERCLASS.tags.length > 0 && (
                  <div className="bg-white rounded-xl shadow-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">
                      Tags
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {CURRENT_MASTERCLASS.tags.map(
                        (tag: string, index: number) => (
                          <span
                            key={index}
                            className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium"
                          >
                            {tag}
                          </span>
                        )
                      )}
                    </div>
                  </div>
                )}

              {/* Resources */}
              {CURRENT_MASTERCLASS.resources &&
                CURRENT_MASTERCLASS.resources.length > 0 && (
                  <div className="bg-white rounded-xl shadow-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">
                      Additional Resources
                    </h3>
                    <div className="space-y-3">
                      {CURRENT_MASTERCLASS.resources.map(
                        (resource: any, index: number) => (
                          <a
                            key={index}
                            href={resource.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                          >
                            <div className="flex-shrink-0 mr-3">
                              {resource.type === "PDF" && (
                                <svg
                                  className="w-6 h-6 text-red-500"
                                  fill="currentColor"
                                  viewBox="0 0 20 20"
                                >
                                  <path
                                    fillRule="evenodd"
                                    d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z"
                                    clipRule="evenodd"
                                  />
                                </svg>
                              )}
                              {resource.type === "LINK" && (
                                <svg
                                  className="w-6 h-6 text-blue-500"
                                  fill="currentColor"
                                  viewBox="0 0 20 20"
                                >
                                  <path
                                    fillRule="evenodd"
                                    d="M12.586 4.586a2 2 0 112.828 2.828l-3 3a2 2 0 01-2.828 0 1 1 0 00-1.414 1.414 4 4 0 005.656 0l3-3a4 4 0 00-5.656-5.656l-1.5 1.5a1 1 0 101.414 1.414l1.5-1.5zm-5 5a2 2 0 012.828 0 1 1 0 101.414-1.414 4 4 0 00-5.656 0l-3 3a4 4 0 105.656 5.656l1.5-1.5a1 1 0 10-1.414-1.414l-1.5 1.5a2 2 0 11-2.828-2.828l3-3z"
                                    clipRule="evenodd"
                                  />
                                </svg>
                              )}
                              {resource.type === "VIDEO" && (
                                <svg
                                  className="w-6 h-6 text-green-500"
                                  fill="currentColor"
                                  viewBox="0 0 20 20"
                                >
                                  <path
                                    fillRule="evenodd"
                                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                                    clipRule="evenodd"
                                  />
                                </svg>
                              )}
                            </div>
                            <div>
                              <p className="font-medium text-gray-900">
                                {resource.title}
                              </p>
                              <p className="text-sm text-gray-500">
                                {resource.type}
                              </p>
                            </div>
                          </a>
                        )
                      )}
                    </div>
                  </div>
                )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default MasterclassDetail;
