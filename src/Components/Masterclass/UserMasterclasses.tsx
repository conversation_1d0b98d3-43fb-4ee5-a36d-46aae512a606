import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link, useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet";
import { getUserEnrolledMasterclasses } from "../../store/actioncreators/masterclassactions";

import LoadingSpinner from "../Loader/LoadingSpinner";

interface UserMasterclassesProps {
  handleLoginModal: () => void;
}

const UserMasterclasses: React.FC<UserMasterclassesProps> = ({
  handleLoginModal,
}) => {
  const dispatch: any = useDispatch();
  const navigate = useNavigate();

  const { USER_MASTERCLASS_LIST, LOADING, ERROR } = useSelector(
    (state: any) => state.MASTERCLASS
  );
  const currentUser = useSelector((state: any) => state.USER.USER);

  const [filter, setFilter] = useState<"all" | "in-progress" | "completed">(
    "all"
  );

  useEffect(() => {
    if (currentUser.admin === -1) {
      handleLoginModal();
      return;
    }

    dispatch(getUserEnrolledMasterclasses("0", "50"));
  }, [dispatch, currentUser.admin]);

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const getProgressColor = (progress: number) => {
    if (progress >= 90) return "bg-green-500";
    if (progress >= 50) return "bg-yellow-500";
    return "bg-blue-500";
  };

  const filteredMasterclasses =
    USER_MASTERCLASS_LIST.enrolledMasterclasses.filter((item: any) => {
      if (filter === "completed") return item.enrollment.isCompleted;
      if (filter === "in-progress")
        return !item.enrollment.isCompleted && item.enrollment.progress > 0;
      return true;
    });

  if (currentUser.admin === -1) {
    return null;
  }

  return (
    <>
      <Helmet>
        <title>Your Masterclasses | GTI</title>
        <meta
          name="description"
          content="View and continue your enrolled masterclasses"
        />
      </Helmet>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm">
          <div className="w-full px-4 sm:px-6 lg:px-8 py-8">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  Your Masterclasses
                </h1>
                <p className="mt-2 text-gray-600">
                  Continue learning with your enrolled masterclasses
                </p>
              </div>

              <div className="mt-4 sm:mt-0">
                <Link
                  to="/masterclass"
                  className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium"
                >
                  Browse More Masterclasses
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="w-full px-4 sm:px-6 lg:px-8 py-8">
          {LOADING ? (
            <div className="flex justify-center items-center h-64">
              <LoadingSpinner />
            </div>
          ) : ERROR ? (
            <div className="text-center py-12">
              <div className="text-red-600 text-lg mb-4">{ERROR}</div>
              <button
                onClick={() =>
                  dispatch(getUserEnrolledMasterclasses("0", "50"))
                }
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Try Again
              </button>
            </div>
          ) : (
            <>
              {/* Filter Tabs */}
              <div className="mb-8">
                <div className="border-b border-gray-200">
                  <nav className="-mb-px flex space-x-8">
                    <button
                      onClick={() => setFilter("all")}
                      className={`py-2 px-1 border-b-2 font-medium text-sm ${
                        filter === "all"
                          ? "border-blue-500 text-blue-600"
                          : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                      }`}
                    >
                      All ({USER_MASTERCLASS_LIST.enrolledMasterclasses.length})
                    </button>
                    <button
                      onClick={() => setFilter("in-progress")}
                      className={`py-2 px-1 border-b-2 font-medium text-sm ${
                        filter === "in-progress"
                          ? "border-blue-500 text-blue-600"
                          : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                      }`}
                    >
                      In Progress (
                      {
                        USER_MASTERCLASS_LIST.enrolledMasterclasses.filter(
                          (item: any) =>
                            !item.enrollment.isCompleted &&
                            item.enrollment.progress > 0
                        ).length
                      }
                      )
                    </button>
                    <button
                      onClick={() => setFilter("completed")}
                      className={`py-2 px-1 border-b-2 font-medium text-sm ${
                        filter === "completed"
                          ? "border-blue-500 text-blue-600"
                          : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                      }`}
                    >
                      Completed (
                      {
                        USER_MASTERCLASS_LIST.enrolledMasterclasses.filter(
                          (item: any) => item.enrollment.isCompleted
                        ).length
                      }
                      )
                    </button>
                  </nav>
                </div>
              </div>

              {/* Masterclass Grid */}
              {filteredMasterclasses.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
                  {filteredMasterclasses.map(
                    ({ masterclass, enrollment }: any) => (
                      <div
                        key={masterclass._id}
                        className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden"
                      >
                        {/* Thumbnail */}
                        <div className="relative">
                          <img
                            src={masterclass.thumbnailImage}
                            alt={masterclass.title}
                            className="w-full h-48 object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = "/api/placeholder/400/300";
                            }}
                          />

                          {/* Progress Badge */}
                          <div className="absolute top-4 right-4">
                            {enrollment.isCompleted ? (
                              <span className="bg-green-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                                ✓ Completed
                              </span>
                            ) : (
                              <span className="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                                {Math.round(enrollment.progress)}%
                              </span>
                            )}
                          </div>

                          {/* Duration */}
                          <div className="absolute bottom-4 right-4 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-sm">
                            {formatDuration(masterclass.duration)}
                          </div>
                        </div>

                        {/* Content */}
                        <div className="p-6">
                          <h3 className="text-xl font-bold text-gray-900 mb-2 line-clamp-2">
                            {masterclass.title}
                          </h3>

                          <p className="text-gray-600 text-sm mb-3">
                            by {masterclass.instructor}
                          </p>

                          <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                            {masterclass.shortDescription}
                          </p>

                          {/* Progress Bar */}
                          <div className="mb-4">
                            <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                              <span>Progress</span>
                              <span>{Math.round(enrollment.progress)}%</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(
                                  enrollment.progress
                                )}`}
                                style={{ width: `${enrollment.progress}%` }}
                              />
                            </div>
                          </div>

                          {/* Enrollment Date */}
                          <p className="text-xs text-gray-500 mb-4">
                            Enrolled on{" "}
                            {new Date(
                              enrollment.enrolledAt
                            ).toLocaleDateString()}
                          </p>

                          {/* Action Buttons */}
                          <div className="flex space-x-2">
                            <button
                              onClick={() =>
                                navigate(
                                  `/masterclass-video/${masterclass._id}`
                                )
                              }
                              className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors"
                            >
                              {enrollment.progress > 0 ? "Continue" : "Start"}
                            </button>
                            <button
                              onClick={() =>
                                navigate(`/masterclass/${masterclass._id}`)
                              }
                              className="flex-1 border border-gray-300 text-gray-700 py-2 px-4 rounded-lg font-medium hover:bg-gray-50 transition-colors"
                            >
                              Details
                            </button>
                          </div>
                        </div>
                      </div>
                    )
                  )}
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
                    <svg
                      className="w-12 h-12 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                      />
                    </svg>
                  </div>

                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    {filter === "all" && "No masterclasses enrolled"}
                    {filter === "in-progress" && "No masterclasses in progress"}
                    {filter === "completed" && "No completed masterclasses"}
                  </h3>

                  <p className="text-gray-600 mb-6">
                    {filter === "all" &&
                      "Start your learning journey by enrolling in a masterclass"}
                    {filter === "in-progress" &&
                      "Complete your enrolled masterclasses or start new ones"}
                    {filter === "completed" &&
                      "Complete some masterclasses to see them here"}
                  </p>

                  <Link
                    to="/masterclass"
                    className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium"
                  >
                    Browse Masterclasses
                  </Link>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default UserMasterclasses;
