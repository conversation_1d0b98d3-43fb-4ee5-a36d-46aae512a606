import React from "react";
import { Form, Formik } from "formik";
import { Helmet } from "react-helmet";

import helpdesk1Img from "../../assests/helpdesk/helpdesk-1.png";
import helpdesk2Img from "../../assests/helpdesk/helpdesk-2.png";
import helpdesk3Img from "../../assests/helpdesk/helpdesk-3.png";
import helpdesk4Img from "../../assests/helpdesk/helpdesk-4.png";
import helpdeskHeroImg from "../../assests/helpdesk/helpdesk-header.png";
import supportImg from "../../assests/helpdesk/helpdesk-support.png";
import helpdeskConnectImg from "../../assests/helpdesk/lets-connect.png";
import CustomModal from "../../shared/CustomModal";
import { helpdeskSchema } from "../validations/contactValidations";
import { notify } from "../../utils";
import { postHelpdesk } from "../../api/helpdesk";
import { title, metaData } from "../constants";

// Add CSS animations
const animationStyles = `
  @keyframes fade-in-up {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fade-in-down {
    from {
      opacity: 0;
      transform: translateY(-30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fade-in-left {
    from {
      opacity: 0;
      transform: translateX(-30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes fade-in-right {
    from {
      opacity: 0;
      transform: translateX(30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .animate-fade-in-up {
    animation: fade-in-up 0.6s ease-out forwards;
  }

  .animate-fade-in-down {
    animation: fade-in-down 0.6s ease-out forwards;
  }

  .animate-fade-in-left {
    animation: fade-in-left 0.6s ease-out forwards;
  }

  .animate-fade-in-right {
    animation: fade-in-right 0.6s ease-out forwards;
  }

  @keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
      transform: translate3d(0,0,0);
    }
    40%, 43% {
      transform: translate3d(0, -8px, 0);
    }
    70% {
      transform: translate3d(0, -4px, 0);
    }
    90% {
      transform: translate3d(0, -2px, 0);
    }
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  .animate-bounce {
    animation: bounce 1.4s infinite;
  }

  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes ping {
    75%, 100% {
      transform: scale(2);
      opacity: 0;
    }
  }

  .animate-ping {
    animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
  }
`;

// Inject styles
if (typeof document !== "undefined") {
  const styleSheet = document.createElement("style");
  styleSheet.type = "text/css";
  styleSheet.innerText = animationStyles;
  document.head.appendChild(styleSheet);
}

interface IConnectCard {
  title: string;
  description: string;
  image: any;
}

const supportData = [
  "Market insight reports.",
  "Local business model development and mentoring.",
  "Market and technology validation – secondary and primary research.",
  "Identification and facilitation of meetings with potential partners/customers/ investors.",
  "Eco-system development and stakeholder identification – mentors, universities, networks, investors, clients, partners, governmental and non-governmental organizations.",
  "Market entry commercialization strategies",
  "Mentorship and support to develop a roadmap for a sustainable market entry.",
];

const connectSectionData: Array<IConnectCard> = [
  {
    title: "Access to the information",
    description:
      "365 days' information services on global technologies, market opportunities, various start-up programs, events/trade-fairs/exhibitions/workshops, funding opportunities, investor information, etc.",
    image: helpdesk1Img,
  },
  {
    title: "Create Connections",
    description:
      "Provide a marketplace for connecting businesses with partners for collaborations, deployment, access to new markets, B2B opportunities with key stakeholders.",
    image: helpdesk2Img,
  },
  {
    title: "Connect with Global Experts",
    description:
      "Provide access to a global network of subject matter experts and mentors who can provide information and mentorship support on tech deployment, business setup, IPR, taxation, regulatory and policies, accounting, etc.  ",
    image: helpdesk3Img,
  },
  {
    title: "One Stop Shop",
    description:
      "From information to deployment – get access to GTI Premium Services and Hands on Support  for internalization.",
    image: helpdesk4Img,
  },
];

const CheckSvg = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
  >
    <path
      d="M11 0C4.9225 0 0 4.9225 0 11C0 17.0775 4.9225 22 11 22C17.0775 22 22 17.0775 22 11C22 4.9225 17.0775 0 11 0ZM16.5 4.895L18.48 6.875L9.625 15.73L4.895 11L6.875 9.02L9.625 11.77L16.5 4.895Z"
      fill="#151E70"
    />
  </svg>
);

const ConnectCard: React.FC<{ data: IConnectCard; index: number }> = ({
  data,
  index,
}) => {
  return (
    <div
      className={`group relative bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100 hover:border-GTI-BLUE-default/20 animate-fade-in-up`}
      style={{ animationDelay: `${index * 150}ms` }}
    >
      {/* Background Gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-GTI-BLUE-default/5 to-purple-500/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

      {/* Content */}
      <div className="relative z-10">
        {/* Image Container */}
        <div className="relative mb-6 overflow-hidden rounded-2xl">
          <img
            src={data.image}
            className="w-full h-48 object-cover transform group-hover:scale-110 transition-transform duration-500"
            alt={data.title}
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        </div>

        {/* Title Badge */}
        <div className="inline-block bg-gradient-to-r from-GTI-BLUE-default to-purple-600 text-white px-4 py-2 rounded-full text-sm font-semibold mb-4 font-popins transform group-hover:scale-105 transition-transform duration-300">
          {data.title}
        </div>

        {/* Description */}
        <p className="text-gray-600 leading-relaxed font-zilla-slab group-hover:text-gray-700 transition-colors duration-300">
          {data.description}
        </p>

        {/* Hover Arrow */}
        <div className="mt-4 flex items-center text-GTI-BLUE-default opacity-0 group-hover:opacity-100 transform translate-x-0 group-hover:translate-x-2 transition-all duration-300">
          <span className="text-sm font-medium mr-2">Learn more</span>
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5l7 7-7 7"
            />
          </svg>
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute top-4 right-4 w-8 h-8 bg-GTI-BLUE-default/10 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      <div className="absolute bottom-4 left-4 w-6 h-6 bg-purple-500/10 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500 delay-100"></div>
    </div>
  );
};

const Helpdesk = () => {
  const [openContactModal, setOpenContactModal] = React.useState(false);

  const onContactClose = () => setOpenContactModal(false);

  const initialValues = {
    firstName: "",
    lastName: "",
    phoneNumber: "",
    email: "",
    message: "",
  };

  const handleSubmit = async (values: typeof initialValues) => {
    try {
      await postHelpdesk(values);
      setOpenContactModal(false);
      notify("Message sent successfully!", "success");
    } catch (err) {
      notify("Something went wrong!", "error");
    }
  };

  return (
    <div className="flex flex-col w-full items-center relative">
      <Helmet>
        <title>{title.HELPDESK}</title>
        <meta
          name="description"
          key="description"
          content={metaData.HELPDESK}
        />
        <meta name="title" key="title" content="Helpdesk" />
        <meta property="og:title" content="Helpdesk" />
        <meta property="og:description" content={metaData.HELPDESK} />
        <meta property="og:image" content={helpdeskHeroImg} />
        <meta
          property="og:url"
          content={`${process.env.REACT_APP_BASE_URL}/helpdesk`}
        />
        <meta property="og:type" content="website" />
        <meta name="twitter:title" content="Helpdesk" />
        <meta name="twitter:description" content={metaData.HELPDESK} />
        <meta name="twitter:image" content={helpdeskHeroImg} />
        <meta name="twitter:card" content="Helpdesk" />
      </Helmet>

      {/* Modern Hero Section */}
      <div className="relative w-full min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/50">
        {/* Background Decorative Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-GTI-BLUE-default/10 to-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-tr from-blue-500/10 to-GTI-BLUE-default/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-GTI-BLUE-default/5 to-purple-500/5 rounded-full blur-3xl"></div>
        </div>

        <div className="relative z-10 w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col lg:flex-row items-center justify-between gap-12 lg:gap-16">
            {/* Content Section */}
            <div className="flex-1 text-center lg:text-left animate-fade-in-up">
              <div className="mb-6">
                <span className="inline-block px-4 py-2 bg-GTI-BLUE-default/10 text-GTI-BLUE-default rounded-full text-sm font-medium mb-4 animate-fade-in-down">
                  🌍 Global Technology Internationalisation
                </span>
                <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight font-popins">
                  GTI® Helpdesk for{" "}
                  <span className="bg-gradient-to-r from-GTI-BLUE-default to-purple-600 bg-clip-text text-transparent">
                    Internationalisation
                  </span>
                </h1>
              </div>

              <div className="space-y-6 text-gray-600 font-zilla-slab">
                <p className="text-lg lg:text-xl leading-relaxed">
                  The objective of the GTI Helpdesk is to help companies with
                  international market access in{" "}
                  <span className="font-semibold text-GTI-BLUE-default">
                    India, EU, UK, USA, Africa, and Southeast Asia
                  </span>
                  . Through the helpdesk, companies looking at
                  internationalisation can avail continuous and consistent
                  support with their queries related to international market
                  access and associated services.
                </p>
                <p className="text-lg leading-relaxed">
                  GTI provides{" "}
                  <span className="font-semibold text-gray-800">
                    online marketplace
                  </span>{" "}
                  and
                  <span className="font-semibold text-gray-800">
                    {" "}
                    virtual connect
                  </span>{" "}
                  with potential partners and stakeholders.
                </p>
              </div>

              <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <button
                  onClick={() => setOpenContactModal(true)}
                  className="group relative px-8 py-4 bg-GTI-BLUE-default text-white rounded-2xl font-semibold text-lg transition-all duration-300 hover:bg-GTI-BLUE-default/90 hover:shadow-xl hover:shadow-GTI-BLUE-default/25 transform hover:-translate-y-1 font-popins"
                >
                  <span className="relative z-10">
                    Get Started - It's FREE!
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-GTI-BLUE-default to-purple-600 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </button>
                <button className="px-8 py-4 border-2 border-gray-300 text-gray-700 rounded-2xl font-semibold text-lg transition-all duration-300 hover:border-GTI-BLUE-default hover:text-GTI-BLUE-default hover:shadow-lg transform hover:-translate-y-1 font-popins">
                  Learn More
                </button>
              </div>
            </div>

            {/* Image Section */}
            <div className="flex-1 relative animate-fade-in-right">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-GTI-BLUE-default/20 to-purple-500/20 rounded-3xl blur-2xl transform rotate-6"></div>
                <img
                  src={helpdeskHeroImg}
                  alt={title.HELPDESK}
                  className="relative z-10 w-full max-w-lg mx-auto rounded-2xl shadow-2xl transform hover:scale-105 transition-transform duration-500"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Connect Section */}
      <div className="w-full py-20 bg-white relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-10 right-10 w-32 h-32 bg-GTI-BLUE-default/5 rounded-full blur-2xl"></div>
          <div className="absolute bottom-10 left-10 w-40 h-40 bg-purple-500/5 rounded-full blur-2xl"></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-fade-in-up">
            <span className="inline-block px-4 py-2 bg-GTI-BLUE-default/10 text-GTI-BLUE-default rounded-full text-sm font-medium mb-4">
              🤝 Connect & Collaborate
            </span>
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight font-popins mb-6">
              Why connect with{" "}
              <span className="bg-gradient-to-r from-GTI-BLUE-default to-purple-600 bg-clip-text text-transparent">
                GTI Helpdesk?
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto font-zilla-slab">
              Discover the comprehensive support and resources available through
              our global network
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12">
            {connectSectionData.map((item, index) => (
              <ConnectCard data={item} key={index} index={index} />
            ))}
          </div>
        </div>
      </div>

      {/* Enhanced Middle Section */}
      <div className="w-full py-20 bg-gradient-to-br from-gray-50 to-blue-50/30 relative overflow-hidden">
        {/* Background Decorations */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 w-24 h-24 bg-GTI-BLUE-default/10 rounded-full blur-xl"></div>
          <div className="absolute bottom-20 right-20 w-32 h-32 bg-purple-500/10 rounded-full blur-xl"></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col lg:flex-row items-center gap-12 lg:gap-16">
            {/* Content */}
            <div className="flex-1 animate-fade-in-left">
              <div className="space-y-6 text-gray-700 font-zilla-slab">
                <p className="text-xl leading-relaxed">
                  Through the helpdesk the companies can connect to{" "}
                  <span className="font-semibold text-GTI-BLUE-default">
                    potential partners
                  </span>{" "}
                  for collaborations and new business opportunities. The
                  Helpdesk aims to become a{" "}
                  <span className="font-semibold text-gray-900">
                    one stop centre
                  </span>{" "}
                  for all queries in the context of expanding business to{" "}
                  <span className="font-semibold text-GTI-BLUE-default">
                    India, EU, UK, USA, Africa, and Southeast Asia
                  </span>
                  .
                </p>
                <p className="text-xl leading-relaxed">
                  Our{" "}
                  <span className="font-semibold text-gray-900">experts</span>{" "}
                  will guide you with the valuable information and relevant
                  networking opportunities. We aim to make your dream to
                  <span className="font-semibold text-GTI-BLUE-default">
                    {" "}
                    scale-up and access new markets
                  </span>{" "}
                  a reality with our support.
                </p>
              </div>
            </div>

            {/* Image */}
            <div className="flex-1 relative animate-fade-in-right">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-GTI-BLUE-default/20 to-purple-500/20 rounded-3xl blur-2xl transform -rotate-6"></div>
                <img
                  src={helpdeskConnectImg}
                  alt="Why connect with GTI Helpdesk?"
                  className="relative z-10 w-full max-w-md mx-auto rounded-2xl shadow-2xl transform hover:scale-105 transition-transform duration-500"
                />
              </div>
            </div>
          </div>

          <div className="mt-12 text-center">
            <button
              onClick={() => setOpenContactModal(true)}
              className="group relative px-8 py-4 bg-GTI-BLUE-default text-white rounded-2xl font-semibold text-lg transition-all duration-300 hover:bg-GTI-BLUE-default/90 hover:shadow-xl hover:shadow-GTI-BLUE-default/25 transform hover:-translate-y-1 font-popins"
            >
              Let’s Connect to make an inquiry. It’s FREE!
            </button>
          </div>
        </div>
      </div>

      {/* Premium Services CTA Section */}
      <div className="w-full py-16 bg-gradient-to-r from-GTI-BLUE-default to-purple-600 relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full blur-2xl"></div>
          <div className="absolute bottom-10 right-10 w-40 h-40 bg-white/5 rounded-full blur-2xl"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-white/5 rounded-full blur-3xl"></div>
        </div>

        <div className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h3 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white leading-tight font-popins mb-6">
            Ready for <span className="text-yellow-300">Advanced Support?</span>
          </h3>
          <p className="text-xl text-blue-100 max-w-4xl mx-auto font-zilla-slab">
            For more advanced internationalisation services and support, you can
            leverage our comprehensive premium services designed to accelerate
            your global expansion
          </p>
        </div>
      </div>

      {/* Enhanced Premium Services Section */}
      <div className="w-full py-20 bg-white relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 right-20 w-32 h-32 bg-GTI-BLUE-default/5 rounded-full blur-2xl"></div>
          <div className="absolute bottom-20 left-20 w-40 h-40 bg-purple-500/5 rounded-full blur-2xl"></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-fade-in-up">
            <span className="inline-block px-4 py-2 bg-GTI-BLUE-default/10 text-GTI-BLUE-default rounded-full text-sm font-medium mb-4">
              🚀 Premium Services
            </span>
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight font-popins mb-6">
              GTI Premium Services and{" "}
              <span className="bg-gradient-to-r from-GTI-BLUE-default to-purple-600 bg-clip-text text-transparent">
                Hands-on Support
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto font-zilla-slab">
              Comprehensive support services designed to accelerate your
              international expansion
            </p>
          </div>

          <div className="flex flex-col lg:flex-row items-center gap-12 lg:gap-16">
            {/* Services List */}
            <div className="flex-1 animate-fade-in-left">
              <div className="grid gap-6">
                {supportData.map((item, index) => (
                  <div
                    key={index}
                    className={`group flex items-start p-6 bg-gradient-to-r from-gray-50 to-blue-50/30 rounded-2xl border border-gray-100 hover:border-GTI-BLUE-default/20 transition-all duration-300 hover:shadow-lg transform hover:-translate-y-1 animate-fade-in-up`}
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <div className="flex-shrink-0 mr-4 mt-1">
                      <div className="w-8 h-8 bg-gradient-to-r from-GTI-BLUE-default to-purple-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <svg
                          className="w-4 h-4 text-white"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M5 13l4 4L19 7"
                          />
                        </svg>
                      </div>
                    </div>
                    <p className="text-lg text-gray-700 font-zilla-slab group-hover:text-gray-900 transition-colors duration-300">
                      {item}
                    </p>
                  </div>
                ))}
              </div>
            </div>

            {/* Image */}
            <div className="flex-1 relative animate-fade-in-right">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-GTI-BLUE-default/20 to-purple-500/20 rounded-3xl blur-2xl transform rotate-3"></div>
                <img
                  src={supportImg}
                  alt="GTI Premium Services and Hands on Support"
                  className="relative z-10 w-full max-w-lg mx-auto rounded-2xl shadow-2xl transform hover:scale-105 transition-transform duration-500"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Final CTA Section */}
      <div className="w-full py-16 bg-gradient-to-br from-gray-50 to-blue-50/30 relative overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-10 left-10 w-24 h-24 bg-GTI-BLUE-default/10 rounded-full blur-xl"></div>
          <div className="absolute bottom-10 right-10 w-32 h-32 bg-purple-500/10 rounded-full blur-xl"></div>
        </div>

        <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h3 className="text-3xl sm:text-4xl font-bold text-gray-900 leading-tight font-popins mb-6">
            Ready to{" "}
            <span className="bg-gradient-to-r from-GTI-BLUE-default to-purple-600 bg-clip-text text-transparent">
              Get Started?
            </span>
          </h3>
          <p className="text-xl text-gray-600 mb-8 font-zilla-slab">
            Connect with our experts today and take the first step towards
            global expansion
          </p>

          <button
            onClick={() => setOpenContactModal(true)}
            className="group relative px-12 py-5 bg-GTI-BLUE-default text-white rounded-2xl font-bold text-xl transition-all duration-300 hover:bg-GTI-BLUE-default/90 hover:shadow-2xl hover:shadow-GTI-BLUE-default/30 transform hover:-translate-y-2 font-popins"
          >
            <span className="relative z-10 flex items-center justify-center">
              Contact Us Now
              <svg
                className="ml-3 w-6 h-6 transform group-hover:translate-x-2 transition-transform duration-300"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M17 8l4 4m0 0l-4 4m4-4H3"
                />
              </svg>
            </span>
            <div className="absolute inset-0 bg-gradient-to-r from-GTI-BLUE-default to-purple-600 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </button>
        </div>
      </div>

      {/* Bottom Spacing */}
      <div className="pb-20"></div>

      {/* Contact Modal */}
      <CustomModal
        className="p-0 max-w-5xl"
        onClose={onContactClose}
        open={openContactModal}
      >
        <div className="flex flex-col lg:flex-row w-full rounded-3xl overflow-hidden bg-white shadow-2xl">
          {/* Left Side - Contact Info */}
          <div className="flex flex-col p-8 lg:p-12 bg-gradient-to-br from-GTI-BLUE-default to-purple-600 justify-center items-center lg:w-2/5 relative overflow-hidden">
            {/* Background Elements */}
            <div className="absolute inset-0">
              <div className="absolute top-10 right-10 w-20 h-20 bg-white/10 rounded-full blur-xl"></div>
              <div className="absolute bottom-10 left-10 w-24 h-24 bg-white/5 rounded-full blur-xl"></div>
            </div>

            <div className="relative z-10 text-center">
              <div className="mb-8">
                <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg
                    className="w-8 h-8 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                    />
                  </svg>
                </div>
                <h2 className="text-white font-popins text-3xl lg:text-4xl font-bold mb-2">
                  Contact Us
                </h2>
                <p className="text-blue-100 font-zilla-slab">
                  Get in touch with our experts
                </p>
              </div>

              <div className="space-y-4 text-white">
                <div className="bg-white/10 rounded-2xl p-4 backdrop-blur-sm">
                  <p className="font-semibold text-lg font-popins">
                    GTI Helpdesk Manager
                  </p>
                  <p className="font-popins text-blue-100">Harsha Lingam</p>
                  <p className="font-popins text-sm text-blue-200 break-all">
                    <EMAIL>
                  </p>
                </div>
              </div>
            </div>
          </div>
          {/* Right Side - Form */}
          <div className="flex flex-col lg:w-3/5 p-8 lg:p-12">
            <div className="mb-8">
              <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 font-popins mb-2">
                Send us a message
              </h3>
              <p className="text-gray-600 font-zilla-slab">
                Fill out the form below and we'll get back to you shortly
              </p>
            </div>

            <Formik
              initialValues={initialValues}
              validationSchema={helpdeskSchema}
              onSubmit={(values: any, { resetForm }) => {
                handleSubmit(values);
                resetForm();
              }}
            >
              {({
                handleChange,
                setFieldValue,
                handleSubmit,
                errors,
                values,
              }) => (
                <>
                  <Form className="space-y-6">
                    {/* Name Fields */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="relative">
                        <input
                          onChange={(e) =>
                            setFieldValue("firstName", e.target.value)
                          }
                          type="text"
                          className="w-full px-4 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default transition-all duration-300 bg-gray-50 focus:bg-white placeholder-gray-400 text-gray-700 font-popins"
                          placeholder="First Name"
                        />
                        {errors.firstName && (
                          <p className="mt-2 text-sm text-red-600 font-popins">
                            {errors.firstName as string}
                          </p>
                        )}
                      </div>

                      <div className="relative">
                        <input
                          onChange={(e) =>
                            setFieldValue("lastName", e.target.value)
                          }
                          type="text"
                          className="w-full px-4 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default transition-all duration-300 bg-gray-50 focus:bg-white placeholder-gray-400 text-gray-700 font-popins"
                          placeholder="Last Name"
                        />
                        {errors.lastName && (
                          <p className="mt-2 text-sm text-red-600 font-popins">
                            {typeof errors.lastName === "object"
                              ? (errors.lastName as any)[0]
                              : (errors.lastName as string)}
                          </p>
                        )}
                      </div>
                    </div>
                    {/* Phone Number */}
                    <div className="relative">
                      <input
                        onChange={(e) =>
                          setFieldValue("phoneNumber", e.target.value)
                        }
                        type="tel"
                        className="w-full px-4 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default transition-all duration-300 bg-gray-50 focus:bg-white placeholder-gray-400 text-gray-700 font-popins"
                        placeholder="Phone Number"
                      />
                      {errors.phoneNumber && (
                        <p className="mt-2 text-sm text-red-600 font-popins">
                          {typeof errors.phoneNumber === "object"
                            ? (errors.phoneNumber as any)[0]
                            : (errors.phoneNumber as string)}
                        </p>
                      )}
                    </div>

                    {/* Email */}
                    <div className="relative">
                      <input
                        onChange={(e) => setFieldValue("email", e.target.value)}
                        type="email"
                        className="w-full px-4 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default transition-all duration-300 bg-gray-50 focus:bg-white placeholder-gray-400 text-gray-700 font-popins"
                        placeholder="Email Address"
                      />
                      {errors.email && (
                        <p className="mt-2 text-sm text-red-600 font-popins">
                          {errors.email as string}
                        </p>
                      )}
                    </div>
                    {/* Message */}
                    <div className="relative">
                      <textarea
                        onChange={(e) =>
                          setFieldValue("message", e.target.value)
                        }
                        rows={4}
                        className="w-full px-4 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default transition-all duration-300 bg-gray-50 focus:bg-white placeholder-gray-400 text-gray-700 font-popins resize-none"
                        placeholder="Tell us about your helpdesk query..."
                      />
                      {errors.message && (
                        <p className="mt-2 text-sm text-red-600 font-popins">
                          {typeof errors.message === "object"
                            ? (errors.message as any)[0]
                            : (errors.message as string)}
                        </p>
                      )}
                    </div>

                    {/* Submit Button */}
                    <div className="pt-4">
                      <button
                        type="submit"
                        className="group relative w-full px-8 py-4 bg-GTI-BLUE-default text-white rounded-2xl font-semibold text-lg transition-all duration-300 hover:bg-GTI-BLUE-default/90 hover:shadow-xl hover:shadow-GTI-BLUE-default/25 transform hover:-translate-y-1 font-popins"
                      >
                        <span className="relative z-10 flex items-center justify-center">
                          Send Message
                          <svg
                            className="ml-2 w-5 h-5 transform group-hover:translate-x-1 transition-transform duration-300"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                            />
                          </svg>
                        </span>
                        <div className="absolute inset-0 bg-gradient-to-r from-GTI-BLUE-default to-purple-600 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      </button>
                    </div>
                  </Form>
                </>
              )}
            </Formik>
          </div>
        </div>
      </CustomModal>
    </div>
  );
};

export default Helpdesk;
