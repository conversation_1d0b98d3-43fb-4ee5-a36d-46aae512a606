import React, { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import "./style.css";
import * as ROUTE from "../Constants/routes";
import {
  HEADER_ARTICLES,
  HEADER_PUBLICATIONS,
  HEADER_EVENTS,
  HEADER_HELPDESK,
  HEADER_INNOVATIONCALL,
  HEADER_OPPORTUNITES,
  HEADER_TECHNOLOGIES,
  HELPDESK,
  HOME,
  INNOVATION,
  OPPORTUNITY,
  TECHNOLOGY,
  HEADER_NEWS,
  HEADER_PREMIUM_SERVICES_DISPLAYER,
  HEADER_PREMIUM_SERVICES_SCOUTER,
} from "../constants";
import { useDetectClickOutside } from "react-detect-click-outside";

interface IHMid {
  handleShow?: any;
  show?: boolean;
}

const H_Mid: React.FC<IHMid> = ({ handleShow, show }) => {
  const location = useLocation();
  const [mediaDrop, setMediaDrop] = useState(false);
  const [premiumServicesDrop, setPremiumServicesDrop] = useState(false);
  const media_ref = useDetectClickOutside({
    onTriggered: () => setMediaDrop(false),
  });
  const premium_services_ref = useDetectClickOutside({
    onTriggered: () => setPremiumServicesDrop(false),
  });

  return (
    <div className="modern-nav-menu">
      <ul className="modern-nav-list">
        <li className="stagger-item">
          <Link
            className={`item micro-bounce focus-ring ${
              location.pathname === TECHNOLOGY ? "active" : ""
            }`}
            to={TECHNOLOGY}
            onClick={() => {
              if (show) handleShow();
            }}
          >
            {HEADER_TECHNOLOGIES}
          </Link>
        </li>
        <li>
          <Link
            className={`item ${
              location.pathname === OPPORTUNITY ? "active" : ""
            }`}
            onClick={() => {
              if (show) handleShow();
            }}
            to={OPPORTUNITY}
          >
            {HEADER_OPPORTUNITES}
          </Link>
        </li>
        <li>
          <Link
            className={`item ${
              location.pathname === INNOVATION ? "active" : ""
            }`}
            onClick={() => {
              if (show) handleShow();
            }}
            to={INNOVATION}
          >
            {HEADER_INNOVATIONCALL}
          </Link>
        </li>
        <li>
          <Link
            className={`item ${location.pathname === HELPDESK ? "active" : ""}`}
            onClick={() => {
              if (show) handleShow();
            }}
            to={HELPDESK}
          >
            {HEADER_HELPDESK}
          </Link>
        </li>
        {/* <li>
          <Link
            className={`item ${
              location.pathname === "/masterclass" ? "active" : ""
            }`}
            onClick={() => {
              if (show) handleShow();
            }}
            to="/masterclass"
          >
            Masterclass
          </Link>
        </li> */}
        <li ref={media_ref}>
          <button
            id="media_drop"
            onClick={() => {
              setMediaDrop(!mediaDrop);
            }}
            data-dropdown-toggle="dropdown"
            className="dropdown-button"
            type="button"
          >
            <Link
              className="text-sm font-medium text-gray-700 hover:text-GTI-BLUE-default transition-colors duration-200"
              to="/articles"
            >
              Media
            </Link>
            <svg
              className={`w-4 h-4 hidden md:block transition-transform duration-300 ${
                mediaDrop ? "rotate-180" : "rotate-0"
              } hover:scale-110`}
              aria-hidden="true"
              fill="none"
              stroke={location.pathname === HOME ? "black" : "white"}
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M19 9l-7 7-7-7"
              ></path>
            </svg>
          </button>
          <div
            id="media_dropdown"
            className={`modern-dropdown ${mediaDrop ? "show" : "hide"}`}
            data-popper-reference-hidden=""
            data-popper-escaped=""
            data-popper-placement="bottom"
          >
            <ul
              className="modern-dropdown-menu"
              aria-labelledby="dropdownDefault"
              onMouseLeave={() => {
                setMediaDrop(false);
              }}
            >
              <li className="modern-dropdown-item-wrapper">
                <Link
                  to={ROUTE.ARTICLE}
                  onClick={() => {
                    setMediaDrop(false);
                    handleShow();
                  }}
                  className="modern-dropdown-item"
                >
                  <span className="dropdown-icon">📰</span>
                  <span className="dropdown-text">{HEADER_ARTICLES}</span>
                </Link>
              </li>
              <li className="modern-dropdown-item-wrapper">
                <Link
                  to={ROUTE.PUBLICATIONS}
                  onClick={() => {
                    setMediaDrop(false);
                    handleShow();
                  }}
                  className="modern-dropdown-item"
                >
                  <span className="dropdown-icon">📚</span>
                  <span className="dropdown-text">{HEADER_PUBLICATIONS}</span>
                </Link>
              </li>
              <li className="modern-dropdown-item-wrapper">
                <Link
                  to={ROUTE.EVENTS}
                  onClick={() => {
                    setMediaDrop(false);
                    handleShow();
                  }}
                  className="modern-dropdown-item"
                >
                  <span className="dropdown-icon">🎯</span>
                  <span className="dropdown-text">{HEADER_EVENTS}</span>
                </Link>
              </li>
              <li className="modern-dropdown-item-wrapper">
                <Link
                  to={ROUTE.NEWS}
                  onClick={() => {
                    setMediaDrop(false);
                  }}
                  className="modern-dropdown-item"
                >
                  <span className="dropdown-icon">📢</span>
                  <span className="dropdown-text">{HEADER_NEWS}</span>
                </Link>
              </li>
            </ul>
          </div>
        </li>
        <li ref={premium_services_ref}>
          <button
            id="premium_services_drop"
            onClick={() => {
              setPremiumServicesDrop(!premiumServicesDrop);
            }}
            data-dropdown-toggle="dropdown"
            className="dropdown-button"
            type="button"
          >
            <Link
              className="text-sm font-medium text-gray-700 hover:text-GTI-BLUE-default transition-colors duration-200"
              to={ROUTE.PREMIUM_SERVICES}
            >
              GTI Services
            </Link>
            <svg
              className={`w-4 h-4 hidden md:block transition-transform duration-300 ${
                premiumServicesDrop ? "rotate-180" : "rotate-0"
              } hover:scale-110`}
              aria-hidden="true"
              fill="none"
              stroke={location.pathname === HOME ? "black" : "white"}
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M19 9l-7 7-7-7"
              ></path>
            </svg>
          </button>
          <div
            id="premium_services_drop"
            className={`modern-dropdown ${
              premiumServicesDrop ? "show" : "hide"
            }`}
            data-popper-reference-hidden=""
            data-popper-escaped=""
            data-popper-placement="bottom"
          >
            <ul
              className="modern-dropdown-menu"
              aria-labelledby="dropdownDefault"
              onMouseLeave={() => {
                setPremiumServicesDrop(false);
              }}
            >
              {/* <li
                onClick={() => {
                  setPremiumServicesDrop(!premiumServicesDrop);
                  handleShow();
                }}
              >
                <Link
                  to={ROUTE.SUPPLY_CHAIN}
                  onClick={() => {
                    handleShow();
                  }}
                  className="font-roboto text-start block py-2 px-4 rounded  text-GTI-BLUE-default  hover:text-slate-500 "
                >
                  {HEADER_SUPPLY_CHAIN}
                </Link>
              </li> */}

              <li className="modern-dropdown-item-wrapper">
                <Link
                  to={ROUTE.DISPLAYER_PREMIUM_SERVICES}
                  onClick={() => {
                    setPremiumServicesDrop(false);
                    handleShow();
                  }}
                  className="modern-dropdown-item"
                >
                  <span className="dropdown-icon">🎨</span>
                  <span className="dropdown-text">
                    {HEADER_PREMIUM_SERVICES_DISPLAYER}
                  </span>
                </Link>
              </li>

              <li className="modern-dropdown-item-wrapper">
                <Link
                  to={ROUTE.SCOUTER_PREMIUM_SERVICES}
                  onClick={() => {
                    setPremiumServicesDrop(false);
                    handleShow();
                  }}
                  className="modern-dropdown-item"
                >
                  <span className="dropdown-icon">🔍</span>
                  <span className="dropdown-text">
                    {HEADER_PREMIUM_SERVICES_SCOUTER}
                  </span>
                </Link>
              </li>
            </ul>
          </div>
        </li>
      </ul>
    </div>
  );
};

export default H_Mid;
